import userEvent from "@testing-library/user-event";
import { type DirectoryJSON, vol } from "memfs";

export * from "@testing-library/react";
export { render } from "./render";
export { userEvent };

/**
 * POPULATES A VIRTUAL FILE SYSTEM WITH A SET OF FILES.
 *
 * @param files - An array of file paths to be added to the virtual file system.
 */
export function fromArray(files: string[]) {
  const filesJSON = files.reduce(
    (accumulator, current) => ((accumulator[current] = Math.random().toString()), accumulator),
    {} as DirectoryJSON
  );

  vol.fromJSON(filesJSON);
}
