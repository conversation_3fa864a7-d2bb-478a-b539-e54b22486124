import { Prisma } from "@prisma/client";

export type Prettify<T> = { [K in keyof T]: T[K] } & {};

export interface LocalImagesPaginationParams {
  orderBy: LocalImageOrderBy;
  where: LocalImageWhere;
  page: number;
}

// LOCAL IMAGE VALIDATORS
export const localImage = Prisma.validator<Prisma.LocalImageDefaultArgs>()({});
export const localImageSelect = Prisma.validator<Prisma.LocalImageSelect>()({
  id: true,
  path: true,
});
export const localImageDefaultArgs = Prisma.validator<Prisma.LocalImageDefaultArgs>()({
  select: localImageSelect,
});

// LOCAL IMAGE PAGINATION TYPES
export type LocalImageModel = Prisma.TypeMap["model"]["LocalImage"];
export type LocalImageArgs = LocalImageModel["operations"]["findMany"]["args"];
export type LocalImageWhere = LocalImageArgs["where"];
export type LocalImageOrderBy = LocalImageArgs["orderBy"];

// SYNC
export type LocalImageIdPath = Prisma.LocalImageGetPayload<typeof localImageDefaultArgs>;
export type SyncProgress = { total: number; progress: number };

export interface LocalImageMetadata {
  thumbnailPath: string;
  path: string;
  width: number;
  height: number;
  created: Date;
  size: number;
  portrait: boolean;
}

// FETCH
export type LocalImage = Prettify<Omit<LocalImagePayload, "created"> & { created: string }>;
export type LocalImagePayload = Prisma.LocalImageGetPayload<typeof localImage>;
export type LocalImageData = {
  data: LocalImage[];
  selectedIds: number[];
  statistics: { selected: number; archived: number; pages: number; total: number };
};

export type FetchLocalImagesParams = Prettify<LocalImagesPaginationParams>;

// LOAD
export type LoadLocalImageParams = Prettify<
  Omit<LocalImagesPaginationParams, "page"> & { id: number }
>;
export type LoadLocalImageResponse = {
  current: LocalImage;
  prev: LocalImage | null;
  next: LocalImage | null;
};
