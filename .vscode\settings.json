{"workbench.tree.indent": 16, "workbench.tree.renderIndentGuides": "always", "terminal.integrated.defaultProfile.osx": "zsh", "terminal.integrated.defaultProfile.windows": "<PERSON><PERSON>", "terminal.integrated.cursorStyle": "line", "terminal.integrated.fontSize": 14, "terminal.integrated.fontWeight": "400", "terminal.integrated.showExitAlert": false, "terminal.integrated.enablePersistentSessions": false, "terminal.integrated.scrollback": 10000, "editor.minimap.enabled": true, "editor.formatOnSave": true, "editor.tabSize": 2, "editor.fontFamily": "'IBM Plex Mono', 'Jetbrains Mono', 'Courier New', monospace", "editor.lineHeight": 1.5, "editor.fontSize": 14, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules\\typescript\\lib", "emmet.includeLanguages": {"postcss": "css"}, "cssVariables.lookupFiles": ["**/*.css", "**/*.scss", "**/*.sass", "**/*.less", "node_modules/@mantine/core/styles.css"]}