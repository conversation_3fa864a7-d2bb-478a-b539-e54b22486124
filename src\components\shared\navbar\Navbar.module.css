.button {
  --ai-size: var(--navbar-width);
  --ai-radius: 0;
  width: 100%;

  /* BUTTON HOVER STATE */
  @mixin hover {
    & svg {
      stroke-width: 1.8;
    }
  }

  /* BUTTON ACTIVE STATE */
  &[data-status="active"] {
    & svg {
      stroke-width: 1.8;
    }
  }

  /* BUTTON DISABLED STATE */
  &:disabled,
  &[data-disabled] {
    background-color: transparent;
    & svg {
      stroke-width: 0.6;
    }
  }

  /* BUTTON LOADING STATE */
  &[data-loading] {
    & span[class*="mantine-ActionIcon-loader"] {
      background-color: transparent;
    }

    & span[class*="mantine-Loader-root"] {
      --ai-size: calc(var(--navbar-width) * 0.7);
      --ai-color: alpha(var(--mantine-color-gray-5), 0.5);
    }
  }
}
