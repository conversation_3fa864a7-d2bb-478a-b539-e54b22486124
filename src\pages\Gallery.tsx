import ImageCard from "@/components/local-gallery/image-card/ImageCard";
import ImageGrid from "@/components/shared/image-grid/ImageGrid";
import type { LocalImage } from "@/helpers/ipc/prisma/local-images/types";
import PageLayout from "@/layouts/PageLayout";
import { useMatches } from "@mantine/core";
import { type HotkeyItem } from "@mantine/hooks";
import { useRef } from "react";

// TODO: Replace with actual data fetching logic
const data: LocalImage[] = [];

export default function Gallery() {
  const refs = useRef<(HTMLDivElement | null)[]>([]);
  const columns = useMatches({ base: 1, xs: 2, sm: 2, md: 3, lg: 4, xl: 5 });

  const hotkeys = (index: number): HotkeyItem[] => {
    return [
      ["ArrowLeft", () => refs.current[index - 1]?.focus()],
      ["ArrowRight", () => refs.current[index + 1]?.focus()],
      ["ArrowUp", () => refs.current[index - columns]?.focus()],
      ["ArrowDown", () => refs.current[index + columns]?.focus()],
    ];
  };

  return (
    <PageLayout>
      <ImageGrid>
        {data.map((image, index) => (
          <ImageCard
            ref={(card) => {
              refs.current[index] = card;
            }}
            key={index}
            src={image.thumbnailPath}
            hotkeys={hotkeys(index)}
            imageCardId={index}
            onOpen={() => {
              /* TODO Implement onEnter functionality */
            }}
          />
        ))}
      </ImageGrid>
    </PageLayout>
  );
}
