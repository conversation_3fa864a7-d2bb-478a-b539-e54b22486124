import { FetchStatus, FilterOption, SortOption } from "@/helpers/constants";
import type { LoadLocalImageResponse } from "@/helpers/ipc/prisma/local-images/types";
import type { RootState } from "@/helpers/types";
import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import {
  deleteLocalImagesReducers,
  fetchLocalImagesReducers,
  resetLocalImagesReducers,
  syncLocalImagesReducers,
  type LocalGalleryState,
  type SetPaginationPayload,
} from ".";

const localGalleryInitialState: LocalGalleryState = {
  fetch: { status: FetchStatus.IDLE, error: null },
  sync: { status: FetchStatus.IDLE, error: null },
  localImages: [],
  localImage: null,
  pagination: { where: { archived: false }, orderBy: { created: "desc" } },
  sortOptions: [SortOption.CREATED_DESC],
  filterOptions: [FilterOption.UNARCHIVED],
  currentPage: 1,
  selectedIds: [],
  statistics: { selected: 0, archived: 0, pages: 0, total: 0 },
};

export const localGallerySlice = createSlice({
  name: "localGallery",
  initialState: localGalleryInitialState,
  reducers: {
    reload: (state) => {
      state.fetch.status = FetchStatus.IDLE;
      state.currentPage = 1;
    },
    select: (state, action: PayloadAction<number>) => {
      state.selectedIds = [...state.selectedIds, action.payload];

      state.localImages = state.localImages.map((image) =>
        image.id === action.payload ? { ...image, selected: true } : image
      );

      state.statistics = { ...state.statistics, selected: ++state.statistics.selected };

      if (state.localImage) {
        state.localImage.current = { ...state.localImage.current, selected: true };
      }
    },
    unselect: (state, action: PayloadAction<number>) => {
      state.selectedIds = state.selectedIds.filter((id) => action.payload !== id);

      state.localImages = state.localImages.map((image) =>
        image.id === action.payload ? { ...image, selected: false } : image
      );

      state.statistics = { ...state.statistics, selected: --state.statistics.selected };

      if (state.localImage) {
        state.localImage.current = { ...state.localImage.current, selected: false };
      }
    },
    archive: (state, action: PayloadAction<number>) => {
      state.localImages = state.localImages.map((image) =>
        image.id === action.payload ? { ...image, archived: true } : image
      );

      state.statistics = { ...state.statistics, archived: ++state.statistics.archived };

      if (state.localImage) {
        state.localImage.current = { ...state.localImage.current, archived: true };
      }
    },
    unarchive: (state, action: PayloadAction<number>) => {
      state.localImages = state.localImages.map((image) =>
        image.id === action.payload ? { ...image, archived: true } : image
      );

      state.statistics = { ...state.statistics, archived: --state.statistics.archived };

      if (state.localImage) {
        state.localImage.current = { ...state.localImage.current, archived: false };
      }
    },
    setPagination: (state, action: PayloadAction<SetPaginationPayload>) => {
      const {
        where = state.pagination.where,
        orderBy = state.pagination.orderBy,
        sortOptions = state.sortOptions,
        filterOptions = state.filterOptions,
        currentPage = state.currentPage,
      } = action.payload;

      state.pagination = { ...state.pagination, where, orderBy };
      state.sortOptions = sortOptions;
      state.filterOptions = filterOptions;
      state.currentPage = currentPage;
    },
    openImage: (state, action: PayloadAction<LoadLocalImageResponse>) => {
      state.localImage = action.payload;
    },
    closeImage: (state) => {
      state.localImage = null;
    },
  },
  extraReducers: (builder) => {
    syncLocalImagesReducers(builder);
    fetchLocalImagesReducers(builder);
    resetLocalImagesReducers(builder);
    deleteLocalImagesReducers(builder);
  },
});

export const localGalleryReducer = localGallerySlice.reducer;
export const {
  reload,
  select,
  archive,
  unselect,
  unarchive,
  openImage,
  closeImage,
  setPagination,
} = localGallerySlice.actions;
export const selectLocalGallery = (state: RootState) => state.localGallery;
