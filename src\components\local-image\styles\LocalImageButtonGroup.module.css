.root {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  justify-content: space-between;
  backdrop-filter: blur(20px);
  mask: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0.8) 20%,
    rgba(0, 0, 0, 0) 40%,
    rgba(0, 0, 0, 0) 60%,
    rgba(0, 0, 0, 0.8) 80%
  );
  margin: 0.5rem;
  border-radius: var(--mantine-radius-md);
  background: rgba(0, 0, 0, 0.8);
  padding: 0.5rem;
}

.checkbox {
  --checkbox-size: calc(var(--checkbox-size-md) * 0.92);
  border-width: 2px;
  border-color: var(--mantine-color-blue-light-color);

  &:not([data-checked="true"]) {
    border-color: var(--mantine-color-blue-light-color);
    background-color: transparent;
  }
}
