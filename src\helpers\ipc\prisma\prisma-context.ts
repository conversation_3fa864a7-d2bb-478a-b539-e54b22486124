import type {
  FetchLocalImagesParams,
  LoadLocalImageParams,
  LoadLocalImageResponse,
  LocalImageData,
  SyncProgress,
} from "@/helpers/ipc/prisma/local-images/types";
import type { Prisma } from "@prisma/client";
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import type { ErrorObject } from "serialize-error";
import * as channel from "./prisma-channels";

const syncLocalImagesContext = {
  syncLocalImages: (): Promise<ErrorObject | number> => ipcRenderer.invoke(channel.SYNC_LOCAL_IMAGES),
  syncLocalImagesAbort: (): void => ipcRenderer.send(channel.SYNC_ABORT_LOCAL_IMAGES),

  onSyncProgressLocalImages: (callback: (args: SyncProgress) => void) => ipcRenderer.on(channel.SYNC_PROGRESS_LOCAL_IMAGES, (event, args) => callback(args)),

  removeSyncProgressLocalImagesListeners: () => ipcRenderer.removeAllListeners(channel.SYNC_PROGRESS_LOCAL_IMAGES),
};

const manageLocalImagesContext = {
  fetchLocalImages: (params: FetchLocalImagesParams): Promise<LocalImageData> => ipcRenderer.invoke(channel.FETCH_LOCAL_IMAGES, params),
  updateLocalImages: (params: Prisma.LocalImageUpdateManyArgs): Promise<void> => ipcRenderer.invoke(channel.UPDATE_LOCAL_IMAGE, params),
  deleteLocalImages: (params: number[]): Promise<void> => ipcRenderer.invoke(channel.DELETE_LOCAL_IMAGES, params),
  resetLocalImages: (): Promise<void> => ipcRenderer.invoke(channel.RESET_LOCAL_IMAGES),
};

const loadLocalImageContext = {
  loadLocalImage: (params: LoadLocalImageParams): Promise<LoadLocalImageResponse> => ipcRenderer.invoke(channel.LOAD_LOCAL_IMAGE, params),
};

export const prismaContext = {
  ...loadLocalImageContext,
  ...syncLocalImagesContext,
  ...manageLocalImagesContext,
};

export function exposePrismaContext() {
  contextBridge.exposeInMainWorld("electronPrisma", prismaContext);
}
