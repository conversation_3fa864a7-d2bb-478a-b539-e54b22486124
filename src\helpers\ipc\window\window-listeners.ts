import { type BrowserWindow, ipc<PERSON>ain, shell } from "electron";
import * as channel from "./window-channels";

export function registerWindowEventListeners(mainWindow: BrowserWindow) {
  ipcMain.handle(channel.WIN_MINIMIZE, () => mainWindow.minimize());
  ipcMain.handle(channel.WIN_MAXIMIZE, () => {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });
  ipcMain.handle(channel.WIN_CLOSE, () => mainWindow.close());
  ipcMain.handle(channel.WIN_OPEN_PATH, (event, path: string) => shell.openPath(path));
}
