import { useDevtools } from "@/helpers/ipc/devtools/devtools-helpers";
import { ActionIcon, type ActionIconProps, type ElementProps, Stack, Tooltip } from "@mantine/core";
import { createLink, type LinkComponent } from "@tanstack/react-router";
import type { LucideIcon } from "lucide-react";
import { House, ImageDown, Images, Settings, Wrench } from "lucide-react";
import { forwardRef } from "react";
import classes from "./Navbar.module.css";

export default function Navbar() {
  const { state, loading, toggle } = useDevtools();

  return (
    <>
      <Stack gap={0}>
        <NavbarButtonLink icon={House} label="Home" to="/" />
        <NavbarButtonLink icon={Images} label="Gallery" to="/gallery" />
        <NavbarButtonLink icon={ImageDown} label="Local Gallery" to="/local-gallery" />
      </Stack>
      <Stack gap={0}>
        <NavbarButton
          icon={Wrench}
          label="Toggle Devtools"
          onClick={toggle}
          loading={loading}
          isActive={state}
        />
        <NavbarButtonLink icon={Settings} label="Settings" to="/settings" />
      </Stack>
    </>
  );
}

// NavbarButtonLink component

interface NavbarButtonProps extends ActionIconProps, ElementProps<"button", keyof ActionIconProps> {
  icon: LucideIcon;
  label: string;
  isActive?: boolean;
}

const NavbarButton = forwardRef<HTMLButtonElement, NavbarButtonProps>((props, ref) => {
  const { icon: Icon, label, isActive, ...rest } = props;

  const activeProps = isActive
    ? { variant: "light", color: "blue.5", "data-status": "active" }
    : { variant: "subtle", color: "gray.5" };

  return (
    <Tooltip label={label} color="gray" position="right">
      <ActionIcon ref={ref} {...activeProps} {...rest} className={classes.button}>
        <Icon size={34} strokeWidth={0.8} />
      </ActionIcon>
    </Tooltip>
  );
});

const NavbarButtonComponent = createLink(NavbarButton);

const NavbarButtonLink: LinkComponent<typeof NavbarButton> = (props) => {
  return (
    <NavbarButtonComponent
      activeProps={{
        variant: "light",
        color: "blue.5",
      }}
      {...props}
    ></NavbarButtonComponent>
  );
};
