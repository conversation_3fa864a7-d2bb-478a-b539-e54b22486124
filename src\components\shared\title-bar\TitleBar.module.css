.root {
  -webkit-app-region: drag;
  display: flex;
  align-items: center;
  height: 100%;

  /* LEFT SIDE */
  & > :first-child {
    flex-grow: 1;
    flex-basis: 0;
    justify-content: flex-start;
  }

  /* RIGHT SIDE */
  & > :last-child {
    flex-grow: 1;
    flex-basis: 0;
    justify-content: flex-end;
  }
}

.title {
  margin-inline: var(--mantine-spacing-md);
  color: var(--mantine-color-dark-2);
  font-style: italic;
  font-variant: small-caps;
  font-weight: 300;
  font-size: var(--mantine-font-size-lg);
  letter-spacing: 3px;
  text-shadow:
    0px 1px 2px rgba(255, 0, 0, 1),
    0px 3px 2px rgba(255, 0, 0, 1),
    0px 4px 8px rgba(255, 0, 0, 1);
  white-space: nowrap;
}

.siteTitle {
  align-items: center;
  gap: 3px;

  & div[class*="mantine-ThemeIcon-root"] {
    background-color: transparent;
    color: var(--mantine-color-dark-3);
  }

  & img[class*="mantine-Image-root"] {
    width: 22px;
    height: 22px;
  }

  & p {
    display: -webkit-box;
    overflow: hidden;
    color: var(--mantine-color-gray-6);
    font-style: italic;
    font-weight: 400;
    font-size: var(--mantine-font-size-md);
    text-overflow: ellipsis;
    word-break: break-all;
    line-clamp: 1;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}

.button {
  --ai-size: calc(var(--app-shell-header-height) - 1px);
  -webkit-app-region: no-drag;
  border-radius: 0;
}

.switch {
  -webkit-app-region: no-drag;
  padding-inline: var(--mantine-spacing-xs);
  padding-block: calc((var(--header-height) - var(--switch-height)) / 2);
  user-select: none;
}
