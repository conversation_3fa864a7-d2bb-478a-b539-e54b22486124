import { PrismaClient } from "@prisma/client";
import fsPromises from "node:fs/promises";

export function getPrisma(id = prismaId): PrismaClient {
  if (prismaPool[id] && prismaId === id) {
    return prismaPool[id];
  } else {
    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
    delete prismaPool[prismaId];
  }

  prismaPool[id] = new PrismaClient({
    datasources: { db: { url: import.meta.env.VITE_DATABASE_URL } },
  });
  prismaId = id;

  return prismaPool[id];
}

export async function existsAsync(path: string) {
  try {
    await fsPromises.access(path, fsPromises.constants.F_OK);
    return true;
  } catch {
    return false;
  }
}

export function chunk<T>(array: T[], size: number): T[][] {
  if (!array.length) {
    return [];
  }

  const head = array.slice(0, size);
  const tail = array.slice(size);

  return [head, ...chunk(tail, size)];
}
