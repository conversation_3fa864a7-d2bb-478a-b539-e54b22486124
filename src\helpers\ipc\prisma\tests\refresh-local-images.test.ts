// Docs https://www.prisma.io/blog/testing-series-1-8eRB5p0Y8o
import { getPrisma } from "@/helpers/ipc/prisma/helpers";
import { syncLocalImages } from "@/helpers/ipc/prisma/operations";
import { fromArray } from "@/tests/unit/utils";
import type { PrismaClient } from "@prisma/client";
import type { BrowserWindow } from "electron";
import { fs, vol } from "memfs";
import { join } from "node:path";
import { mockDeep } from "vitest-mock-extended";

vi.mock("@/helpers/ipc/prisma/helpers", () => ({ getPrisma: vi.fn() }));
vi.mock("node:fs", async () => {
  const { fs } = await vi.importActual<typeof import("memfs")>("memfs");
  return { default: { ...fs, existsSync: fs.existsSync } };
});
vi.mock("node:fs/promises", async () => {
  const { fs } = await vi.importActual<typeof import("memfs")>("memfs");
  return { default: { ...fs.promises, readdir: fs.promises.readdir } };
});

describe("syncLocalImages tests", () => {
  let mockPrisma: ReturnType<typeof mockDeep<PrismaClient>>;

  const LOCAL_IMAGES_DIR = import.meta.env.VITE_LOCAL_IMAGES_DIR;

  beforeEach(() => {
    mockPrisma = mockDeep<PrismaClient>();
    (getPrisma as ReturnType<typeof vi.fn>).mockReturnValue(mockPrisma);

    // Create local thumbnails directory
    fs.mkdirSync(LOCAL_IMAGES_DIR, { recursive: true });
  });

  afterEach(() => {
    vi.unstubAllEnvs();
    vi.resetAllMocks();
    vi.resetModules();
    vol.reset();
  });

  test("should remove images from database if no images are found on disk.", async () => {
    const localImageDb = [
      { id: 1, path: join(LOCAL_IMAGES_DIR, "001.jpg") },
      { id: 2, path: join(LOCAL_IMAGES_DIR, "002.jpg") },
    ];

    const mockWindow = mockDeep<BrowserWindow>();
    const mockGetImagesFromFileSystem = vi.fn().mockResolvedValue([]);
    const mockRemoveNotFoundImagesFromDatabase = vi.fn().mockResolvedValue(undefined);
    const mockCreateNewImages = vi.fn().mockResolvedValue(localImageDb);
    const mockPrismaFindMany = vi.fn().mockResolvedValue(localImageDb);
    mockPrisma.localImage.findMany.mockImplementation(mockPrismaFindMany);

    await syncLocalImages({
      window: mockWindow,
      getImagesFromFileSystem: mockGetImagesFromFileSystem,
      removeNotFoundImagesFromDatabase: mockRemoveNotFoundImagesFromDatabase,
      createNewImages: mockCreateNewImages,
    });

    expect(mockGetImagesFromFileSystem).toHaveResolvedWith([]);
    expect(mockRemoveNotFoundImagesFromDatabase).toHaveBeenCalledWith(localImageDb);
    expect(mockCreateNewImages).toHaveBeenCalledWith({
      window: mockWindow,
      images: [],
      chunkSize: 8,
    });
  });

  test("should create images in database if new images are found on disk.", async () => {
    const localImages = [
      join(LOCAL_IMAGES_DIR, "001.jpg"),
      join(LOCAL_IMAGES_DIR, "002.jpg"),
      join(LOCAL_IMAGES_DIR, "003.jpg"),
    ];

    fromArray(localImages);

    const mockWindow = mockDeep<BrowserWindow>();
    const mockRemoveNotFoundImagesFromDatabase = vi.fn().mockResolvedValue(undefined);
    const mockCreateNewImages = vi.fn().mockResolvedValue(undefined);
    const mockPrismaFindMany = vi.fn().mockResolvedValue([]);
    mockPrisma.localImage.findMany.mockImplementation(mockPrismaFindMany);

    await syncLocalImages({
      window: mockWindow,
      removeNotFoundImagesFromDatabase: mockRemoveNotFoundImagesFromDatabase,
      createNewImages: mockCreateNewImages,
    });

    expect(mockRemoveNotFoundImagesFromDatabase).toHaveBeenCalledWith([]);
    expect(mockCreateNewImages).toHaveBeenCalledWith({
      window: mockWindow,
      images: localImages,
      chunkSize: 8,
    });
  });

  test("should sync images between database and file system.", async () => {
    const localImagesFileSystem = [
      join(LOCAL_IMAGES_DIR, "001.jpg"),
      join(LOCAL_IMAGES_DIR, "002.jpg"),
      join(LOCAL_IMAGES_DIR, "003.png"),
    ];

    const localImagesFileSystemNew = [
      join(LOCAL_IMAGES_DIR, "005.jpeg"), // New
      join(LOCAL_IMAGES_DIR, "006.jpeg"), // New
    ];

    const localImageDb = [
      { id: 1, path: join(LOCAL_IMAGES_DIR, "001.jpg") },
      { id: 2, path: join(LOCAL_IMAGES_DIR, "002.jpg") },
      { id: 3, path: join(LOCAL_IMAGES_DIR, "003.png") },
      { id: 4, path: join(LOCAL_IMAGES_DIR, "004.png") }, // Deleted
    ];

    fromArray([...localImagesFileSystem, ...localImagesFileSystemNew]);

    const mockWindow = mockDeep<BrowserWindow>();
    const mockRemoveNotFoundImagesFromDatabase = vi.fn().mockResolvedValue(undefined);
    const mockCreateNewImages = vi.fn().mockResolvedValue(undefined);
    const mockPrismaFindMany = vi.fn().mockResolvedValue(localImageDb);
    mockPrisma.localImage.findMany.mockImplementation(mockPrismaFindMany);

    await syncLocalImages({
      window: mockWindow,
      removeNotFoundImagesFromDatabase: mockRemoveNotFoundImagesFromDatabase,
      createNewImages: mockCreateNewImages,
    });

    expect(mockRemoveNotFoundImagesFromDatabase).toHaveBeenCalledWith(localImageDb);
    expect(mockCreateNewImages).toHaveBeenCalledWith({
      window: mockWindow,
      images: localImagesFileSystemNew,
      chunkSize: 8,
    });
  });
});
