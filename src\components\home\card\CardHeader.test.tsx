import CardHeader from "@/components/home/<USER>/CardHeader";
import { cleanup, render, screen, userEvent } from "@/tests/unit/utils";

const mockNavigate = vi.fn();
const mockProps = { id: "100", url: "https://example.com", date: "2025-05-25T12:30:00.000Z" };

describe("CardHeader component", () => {
  afterEach(() => {
    vi.resetAllMocks();
    cleanup();
  });

  test("renders CardHeader with all required props", async () => {
    await render(<CardHeader {...mockProps} />);

    expect(screen.getByText(mockProps.url)).toBeInTheDocument();
  });

  test("shows the default Globe icon when icon prop is missing", async () => {
    const { container } = await render(<CardHeader {...mockProps} />);

    expect(container.querySelector("svg.lucide.lucide-globe")).toBeInTheDocument();
  });

  test("shows the favicon when icon prop is provided", async () => {
    const props = { ...mockProps, icon: "R0lGODlhAQABAAAAACw=" };

    const { container } = await render(<CardHeader {...props} />);

    expect(container.querySelector("img")).toBeInTheDocument();
    expect(container.querySelector("img")?.getAttribute("src")).toEqual(
      `data:image/png;base64, ${props.icon}`
    );
  });

  test("CardLink receives correct route parameters", async () => {
    vi.mock(import("@tanstack/react-router"), async (importOriginal) => {
      const actual = await importOriginal();
      return {
        ...actual,
        createLink: (_) => (props) => {
          const { children, ...rest } = props;

          return (
            <a href="#" onClick={() => mockNavigate(rest)}>
              {children as React.ReactNode}
            </a>
          );
        },
      };
    });

    await render(<CardHeader {...mockProps} />);

    const anchor = screen.getByText(mockProps.url);
    await userEvent.click(anchor);

    expect(anchor).toBeInTheDocument();
    expect(mockNavigate).toHaveBeenCalledWith({
      to: "/gallery",
      search: { id: mockProps.id, favicon: undefined, url: mockProps.url },
    });
  });
});
