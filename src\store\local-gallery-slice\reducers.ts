import { FetchStatus } from "@/helpers/constants";
import { type ActionReducerMapBuilder } from "@reduxjs/toolkit";
import {
  deleteLocalImages,
  fetchLocalImages,
  resetLocalImages,
  syncLocalImages,
  type LocalGalleryState,
} from ".";

type Builder = ActionReducerMapBuilder<LocalGalleryState>;

export const fetchLocalImagesReducers = (builder: Builder) => {
  builder.addCase(fetchLocalImages.pending, (state) => {
    state.fetch.status = FetchStatus.LOADING;
  });
  builder.addCase(fetchLocalImages.fulfilled, (state, { payload, meta }) => {
    state.fetch.status = FetchStatus.SUCCEEDED;

    state.localImages = payload.data;
    state.selectedIds = payload.selectedIds;
    state.statistics = payload.statistics;

    if (meta.arg) {
      state.currentPage = meta.arg;
    }
  });
  builder.addCase(fetchLocalImages.rejected, (state, { error }) => {
    state.fetch.status = FetchStatus.FAILED;
    state.fetch.error = error;
  });
};

export const syncLocalImagesReducers = (builder: Builder) => {
  builder.addCase(syncLocalImages.pending, (state) => {
    state.sync.status = FetchStatus.LOADING;
  });
  builder.addCase(syncLocalImages.fulfilled, (state) => {
    state.sync.status = FetchStatus.SUCCEEDED;
  });
  builder.addCase(syncLocalImages.rejected, (state, { error }) => {
    state.sync.status = FetchStatus.FAILED;
    state.sync.error = error;
  });
};

export const deleteLocalImagesReducers = (builder: Builder) => {
  builder.addCase(deleteLocalImages.pending, (state) => {
    state.fetch.status = FetchStatus.LOADING;
  });
  builder.addCase(deleteLocalImages.fulfilled, (state, { payload }) => {
    state.fetch.status = FetchStatus.SUCCEEDED;

    state.localImages = payload.data;
    state.selectedIds = payload.selectedIds;
    state.statistics = payload.statistics;
  });
  builder.addCase(deleteLocalImages.rejected, (state, { error }) => {
    state.fetch.status = FetchStatus.FAILED;
    state.fetch.error = error;
  });
};

export const resetLocalImagesReducers = (builder: Builder) => {
  builder.addCase(resetLocalImages.pending, (state) => {
    state.fetch.status = FetchStatus.LOADING;
  });
  builder.addCase(resetLocalImages.fulfilled, (state) => {
    state.fetch.status = FetchStatus.SUCCEEDED;

    state.localImages = [];
    state.selectedIds = [];
    state.statistics = { selected: 0, archived: 0, pages: 0, total: 0 };
  });
  builder.addCase(resetLocalImages.rejected, (state, { error }) => {
    state.fetch.status = FetchStatus.FAILED;
    state.fetch.error = error;
  });
};
