import CardButton from "@/components/home/<USER>/CardButton";
import CardHeader from "@/components/home/<USER>/CardHeader";
import { Badge, Flex, LoadingOverlay, Paper } from "@mantine/core";
import { GripVertical, Pencil, RefreshCw } from "lucide-react";
import classes from "./Card.module.css";

interface CardProps {
  id: string;
  url: string;
  date: string;
  icon?: string;
  unvisited: number;
}

export default function Card(props: CardProps) {
  return (
    <Paper shadow="md" className={classes.card}>
      <LoadingOverlay
        visible={false}
        loaderProps={{ children: "Downloading..." }}
        transitionProps={{ transition: "fade", duration: 200 }}
      />
      <CardHeader {...props} />
      <Flex>
        <CardButton icon={RefreshCw} />
        <CardButton icon={Pencil} />
        <CardButton icon={GripVertical} disabled />
      </Flex>
      {!!props.unvisited && <Badge>{props.unvisited}</Badge>}
    </Paper>
  );
}
