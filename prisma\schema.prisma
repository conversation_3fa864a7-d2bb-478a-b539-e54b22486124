generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("VITE_DATABASE_URL")
}

model LocalImage {
  id            Int      @id @default(autoincrement())
  path          String   @unique
  thumbnailPath String   @unique
  created       DateTime
  height        Int
  width         Int
  size          Float
  selected      <PERSON><PERSON><PERSON>  @default(false)
  archived      Boolean  @default(false)
  portrait      <PERSON>ole<PERSON>  @default(false)
}
