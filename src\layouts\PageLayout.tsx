import { Footer } from "@/components/shared/footer";
import { AppShell, Portal, ScrollArea, Stack } from "@mantine/core";
import { createRef } from "react";
import classes from "./PageLayout.module.css";

type PageLayoutProps = {
  children: React.ReactNode;
  footer?: {
    leftSection?: React.ReactNode;
    centerSection?: React.ReactNode;
    rightSection?: React.ReactNode;
  };
};

export default function PageLayout({ children, footer }: PageLayoutProps) {
  const viewport = createRef<HTMLDivElement>();

  return (
    <>
      <AppShell.Main>
        <ScrollArea.Autosize scrollbars="y" scrollbarSize={20} viewportRef={viewport}>
          <div>
            <Stack className={classes.wrapper}>{children}</Stack>
          </div>
        </ScrollArea.Autosize>
      </AppShell.Main>
      <AppShell.Footer>
        <Portal target="footer" reuseTargetNode>
          <Footer>
            <div>{footer?.leftSection}</div>
            <div>{footer?.centerSection}</div>
            <div>{footer?.rightSection}</div>
          </Footer>
        </Portal>
      </AppShell.Footer>
    </>
  );
}
