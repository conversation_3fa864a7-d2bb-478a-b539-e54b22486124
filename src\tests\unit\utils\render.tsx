import { Man<PERSON>Provider } from "@mantine/core";
import { createRootRoute, createRouter, RouterProvider } from "@tanstack/react-router";
import { act, render as testingLibraryRender } from "@testing-library/react";

export async function render(ui: React.ReactNode) {
  const rootRoute = createRootRoute({ component: () => <>{ui}</> });
  const router = createRouter({ defaultPendingMinMs: 0, routeTree: rootRoute.addChildren([]) });

  const renderResult = testingLibraryRender(<RouterProvider router={router} />, {
    wrapper: ({ children }: { children: React.ReactNode }) => (
      <MantineProvider>{children}</MantineProvider>
    ),
  });

  await act(() => router.navigate({ to: "/" }));

  return renderResult;
}
