import type { FetchStatus, FilterOption, SortOption } from "@/helpers/constants";
import type {
  LoadLocalImageResponse,
  LocalImage,
  LocalImageOrderBy,
  LocalImageWhere,
} from "@/helpers/ipc/prisma/local-images/types";
import type { SerializedError } from "@reduxjs/toolkit";

export type SetPaginationPayload = Partial<{
  where: LocalImageWhere;
  orderBy: LocalImageOrderBy;
  sortOptions: SortOption[];
  filterOptions: FilterOption[];
  currentPage: number;
}>;

export interface LocalGalleryState {
  fetch: { status: FetchStatus; error: SerializedError | null };
  sync: { status: FetchStatus; error: SerializedError | null };
  localImage: LoadLocalImageResponse | null;
  localImages: LocalImage[];
  pagination: { where: LocalImageWhere; orderBy: LocalImageOrderBy };
  sortOptions: SortOption[];
  filterOptions: FilterOption[];
  currentPage: number;
  selectedIds: number[];
  statistics: { selected: number; archived: number; pages: number; total: number };
}
