import type { LocalImage } from "@/helpers/ipc/prisma/local-images/types";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { closeImage, openImage, selectLocalGallery } from "@/store/local-gallery-slice";
import { FocusTrap, Modal } from "@mantine/core";
import { useDisclosure, useHotkeys } from "@mantine/hooks";
import { useCallback, useEffect, useRef } from "react";
import {
  TransformComponent,
  TransformWrapper,
  type ReactZoomPanPinchContentRef,
} from "react-zoom-pan-pinch";
import { LocalImageButtonGroup } from "./LocalImageButtonGroup";
import classes from "./styles/LocalImageModal.module.css";

interface LocalImageProps {
  onSelect: (id: number) => void;
  onDelete: (ids: number[]) => void;
  onArchive: (image: LocalImage) => void;
}

export default function LocalImageModal({ onSelect, onDelete, onArchive }: LocalImageProps) {
  const ref = useRef<ReactZoomPanPinchContentRef | null>(null);
  const { pagination, localImage } = useAppSelector(selectLocalGallery);
  const [opened, handlers] = useDisclosure(false);
  const dispatch = useAppDispatch();

  const onDeleteHandler = () => {
    if (localImage) {
      const { current, next } = localImage;
      onDelete([current.id]);

      if (next) {
        window.electronPrisma.loadLocalImage({ ...pagination, id: next.id }).then((response) => {
          dispatch(openImage(response));
          ref.current?.resetTransform(0);
        });
      } else {
        dispatch(closeImage());
      }
    }
  };

  useEffect(() => {
    if (localImage) {
      handlers.open();
    } else {
      handlers.close();
    }
  }, [localImage]);

  useHotkeys(
    localImage
      ? [
          [
            "ArrowRight",
            () => {
              if (localImage.next) {
                window.electronPrisma
                  .loadLocalImage({ ...pagination, id: localImage.next.id })
                  .then((response) => dispatch(openImage(response)));
                ref.current?.resetTransform(0);
              }
            },
          ],
          [
            "ArrowLeft",
            () => {
              if (localImage.prev) {
                window.electronPrisma
                  .loadLocalImage({ ...pagination, id: localImage.prev.id })
                  .then((response) => dispatch(openImage(response)));
                ref.current?.resetTransform(0);
              }
            },
          ],
          ["mod+A", () => onArchive(localImage.current)],
          ["Delete", () => onDeleteHandler()],
          ["Space", () => onSelect(localImage.current.id)],
        ]
      : []
  );

  const Image = useCallback(() => {
    return localImage?.current.path ? (
      <img
        loading="lazy"
        alt={localImage.current.path}
        height={localImage.current.height}
        src={`app:///${localImage.current.path}`}
      />
    ) : null;
  }, [localImage]);

  return (
    <Modal
      size="auto"
      returnFocus
      opened={opened}
      withCloseButton={false}
      onClose={() => dispatch(closeImage())}
      className={classes.localImage}
    >
      <FocusTrap.InitialFocus />
      <TransformWrapper ref={ref} wheel={{ smoothStep: 0.005 }}>
        <TransformComponent>
          <Image />
        </TransformComponent>
      </TransformWrapper>

      {localImage && (
        <LocalImageButtonGroup
          image={localImage.current}
          zoomIn={() => ref.current?.zoomIn()}
          zoomOut={() => ref.current?.resetTransform(0)}
          onDelete={() => onDeleteHandler()}
          onSelect={() => onSelect(localImage.current.id)}
          onArchive={() => onArchive(localImage.current)}
        />
      )}
    </Modal>
  );
}
