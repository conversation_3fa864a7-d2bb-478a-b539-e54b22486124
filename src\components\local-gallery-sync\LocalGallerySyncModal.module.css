.modal {
  & div[class*="mantine-Modal-inner"] {
    padding-top: calc(var(--header-height) + 2vh);
  }

  & section[class*="mantine-Modal-content"] {
    border-radius: var(--mantine-radius-sm);
    overflow-y: hidden;
  }

  & div[class*="mantine-Modal-body"] {
    padding: 0;
  }

  & div[class*="mantine-Modal-overlay"] {
    backdrop-filter: blur(3px);
    background-color: rgba(0, 0, 0, 0.55);
  }
}

.buttons {
  justify-content: center;
  gap: var(--mantine-spacing-md);
  padding-inline: var(--mantine-spacing-md);
  padding-bottom: var(--mantine-spacing-md);
}
