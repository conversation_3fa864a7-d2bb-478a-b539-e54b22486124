import { <PERSON>rfvanViewIcon } from "@/components/shared/custom-icons";
import type { LocalImage } from "@/helpers/ipc/prisma/local-images/types";
import { Checkbox, Flex, Group, Stack, Text } from "@mantine/core";
import { Heart, Info, Trash2, ZoomIn, ZoomOut } from "lucide-react";
import { useCallback } from "react";
import { LocalImageButton } from "./LocalImageButton";
import classes from "./styles/LocalImageButtonGroup.module.css";

interface ButtonGroupProps {
  image: LocalImage;
  zoomIn: VoidFunction;
  zoomOut: VoidFunction;
  onSelect: () => void;
  onDelete: () => void;
  onArchive: () => void;
}

export function LocalImageButtonGroup(props: ButtonGroupProps) {
  const { image, zoomIn, zoomOut, onSelect, onArchive, onDelete } = props;

  const ArchivedButton = useCallback(() => {
    return (
      <LocalImageButton
        label="Archive"
        Icon={Heart}
        iconProps={image.archived ? { fill: "var(--mantine-color-blue-light-color)" } : {}}
        onClick={() => onArchive()}
      />
    );
  }, [image.archived]);

  return (
    <Flex className={classes.root}>
      <Group>
        <LocalImageButton label="Zoom to fit" Icon={ZoomOut} onClick={() => zoomOut()} />
        <LocalImageButton label="Zoom" Icon={ZoomIn} onClick={() => zoomIn()} />
        <Checkbox.Indicator
          variant="outline"
          checked={image.selected}
          onClick={() => onSelect()}
          className={classes.checkbox}
        />
        <ArchivedButton />
        <LocalImageButton
          label="Open in IrfvanView"
          Icon={IrfvanViewIcon}
          iconProps={{
            style: { width: 26, height: 26 },
          }}
        />
        <LocalImageButton
          label={
            <Stack gap={2}>
              <Text ff="monospace">Filepath : {image.path}</Text>
              <Text ff="monospace">Thumbnail : {image.thumbnailPath}</Text>
              <Text ff="monospace">Size : {bytesToSize(image.size)}</Text>
            </Stack>
          }
          Icon={Info}
        />
      </Group>
      <Group>
        <LocalImageButton
          label="Delete"
          Icon={Trash2}
          style={{ color: "red" }}
          onClick={() => onDelete()}
        />
      </Group>
    </Flex>
  );
}

function bytesToSize(bytes: number) {
  if (bytes === 0) return "0 B";

  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const sizeIndex = Math.floor(Math.log(bytes) / Math.log(1024));

  return sizeIndex === 0
    ? `${bytes} ${sizes[sizeIndex]})`
    : `${(bytes / 1024 ** sizeIndex).toFixed(1)} ${sizes[sizeIndex]}`;
}
