import registerListeners from "@/helpers/ipc/listeners-register";
import { app, BrowserWindow } from "electron";
import path from "node:path";

const createWindow = () => {
  const mainWindow = new BrowserWindow({
    width: 1600,
    height: 1000,
    webPreferences: { preload: path.join(__dirname, "preload.js") },
    titleBarStyle: "hidden",
  });

  // Initialize global variables
  globalThis.prismaId = 0;
  globalThis.prismaPool = [];

  registerListeners(mainWindow);

  MAIN_WINDOW_VITE_DEV_SERVER_URL
    ? mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL)
    : mainWindow.loadFile(path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`));
};

app.whenReady().then(createWindow);

// MacOS only
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// MacOS only
app.on("activate", () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
