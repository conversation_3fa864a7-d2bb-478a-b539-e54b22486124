import type { ActionIconProps, ElementProps } from "@mantine/core";
import { ActionIcon, Tooltip } from "@mantine/core";
import type { LucideIcon } from "lucide-react";
import React, { useCallback } from "react";

type ButtonProps = ElementProps<"button", keyof ActionIconProps>;
type ImageIcon = React.ComponentType<React.HTMLProps<HTMLImageElement>>;

interface LocalImageButtonProps extends ActionIconProps, ButtonProps {
  Icon: LucideIcon | ImageIcon;
  label: React.ReactNode;
  iconProps?: React.ComponentProps<LucideIcon> | React.ComponentProps<ImageIcon>;
}

export function LocalImageButton(props: LocalImageButtonProps) {
  const {
    Icon,
    label,
    iconProps,
    variant = "transparent",
    size = "lg",
    color = "blue",
    ...rest
  } = props;

  const IconComponent = useCallback(() => {
    return isLucideIcon(Icon) ? (
      <Icon {...(iconProps as React.ComponentProps<LucideIcon>)} />
    ) : (
      <Icon {...(iconProps as React.ComponentProps<ImageIcon>)} />
    );
  }, [Icon]);

  return (
    <Tooltip label={label} position="top" color="gray" withArrow>
      <ActionIcon variant={variant} size={size} color={color} tabIndex={-1} {...rest}>
        <IconComponent />
      </ActionIcon>
    </Tooltip>
  );
}

function isLucideIcon(icon: React.ComponentType): icon is LucideIcon {
  // Determine if a given React component is a LucideIcon.
  return icon.displayName !== undefined;
}
