import { Loader, Modal } from "@mantine/core";
import { useEffect, useState } from "react";
import classes from "./LoaderModal.module.css";

export default function LoaderModal({ isLoading }: { isLoading: boolean }) {
  const [openModal, setOpenModal] = useState(false);

  useEffect(() => {
    setOpenModal(isLoading);
  }, [isLoading]);

  return (
    <Modal
      opened={openModal}
      onClose={() => setOpenModal(false)}
      className={classes.loader}
      withCloseButton={false}
      returnFocus
      centered
    >
      <Loader color="blue" size="xl" type="bars" />
    </Modal>
  );
}
