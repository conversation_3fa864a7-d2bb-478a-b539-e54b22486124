{"name": "viper-app", "productName": "viper-app", "version": "0.0.1", "description": "Viper App", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "cross-env NODE_OPTIONS=--disable-warning=DEP0174 electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint .", "format": "prettier --write .", "test": "vitest run --silent"}, "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.8.0", "@electron-forge/maker-zip": "^7.8.0", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron/fuses": "^1.8.0", "@eslint/js": "^9.25.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/redux-logger": "^3.0.13", "@vitejs/plugin-react": "^4.4.1", "babel-plugin-react-compiler": "^19.1.0-rc.1", "cross-env": "^7.0.3", "electron": "37.1.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-compiler": "^19.1.0-rc.1", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "jsdom": "^26.1.0", "memfs": "^4.17.2", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.5.3", "prettier-plugin-css-order": "^2.1.2", "prisma": "^6.7.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "typescript-plugin-css-modules": "^5.2.0", "vite": "^7.0.0", "vitest": "^3.1.4", "vitest-mock-extended": "^3.1.0"}, "dependencies": {"@mantine/core": "^8.1.2", "@mantine/hooks": "^8.1.2", "@prisma/client": "^6.7.0", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-router": "^1.117.0", "lucide-react": "^0.525.0", "p-queue": "^8.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-zoom-pan-pinch": "^3.7.0", "redux-logger": "^3.0.6", "serialize-error": "^12.0.0", "sharp": "^0.34.1", "uuid": "^11.1.0"}}