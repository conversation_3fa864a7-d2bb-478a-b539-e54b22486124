import { chunk } from "@/helpers/ipc/prisma/helpers";

describe("helper tests", () => {
  test("should split an array of images into chunks of given size.", () => {
    const images = [
      "image001.jpg",
      "image002.jpg",
      "image003.jpg",
      "image004.jpg",
      "image005.jpg",
      "image006.jpg",
      "image007.jpg",
    ];

    const result = chunk(images, 2);

    expect(result).toEqual([
      ["image001.jpg", "image002.jpg"],
      ["image003.jpg", "image004.jpg"],
      ["image005.jpg", "image006.jpg"],
      ["image007.jpg"],
    ]);
  });
});
