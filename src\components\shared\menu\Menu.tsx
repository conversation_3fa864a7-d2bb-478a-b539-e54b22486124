import {
  type ElementProps,
  type MenuItemProps,
  type MenuProps as MenuMantineProps,
  Menu as MenuMantine,
} from "@mantine/core";
import classes from "./Menu.module.css";

export interface ItemProps extends MenuItemProps, ElementProps<"button", keyof MenuItemProps> {
  text?: string;
  divider?: boolean;
}

interface MenuProps extends Pick<MenuMantineProps, "opened" | "onChange"> {
  children: React.ReactNode;
  items: ItemProps[];
  customItems?: React.ReactNode;
}

export default function Menu({ children, items, customItems, ...props }: MenuProps) {
  return (
    <MenuMantine width={450} position="top-end" offset={10} withOverlay {...props}>
      <MenuMantine.Target>{children}</MenuMantine.Target>

      <MenuMantine.Dropdown className={classes.menu}>
        {customItems}
        {items.map((item, index) =>
          item.divider ? (
            <MenuMantine.Divider key={index} />
          ) : (
            <MenuMantine.Item key={index} {...item}>
              {item.text}
            </MenuMantine.Item>
          )
        )}
      </MenuMantine.Dropdown>
    </MenuMantine>
  );
}

Menu.Label = MenuMantine.Label;
Menu.Divider = MenuMantine.Divider;
Menu.Sub = MenuMantine.Sub;
