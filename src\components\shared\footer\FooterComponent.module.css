.icon {
  width: var(--button-height-md, 42px);
  height: var(--button-height-md, 42px);

  & > svg {
    color: var(--mantine-color-gray-3);
    stroke-width: 1.6;
    width: 24px;
    height: 24px;
  }

  &:hover {
    & svg {
      color: white;
      stroke-width: 2;
    }
  }
}

.button {
  & span[class*="mantine-Button-label"] {
    color: var(--mantine-color-gray-3);
    letter-spacing: 1.1px;
  }

  & svg {
    color: var(--mantine-color-gray-3);
    stroke-width: 1.2;
    width: 20px;
    height: 20px;
  }

  &:hover {
    & span[class*="mantine-Button-label"] {
      color: white;
    }

    & svg {
      color: white;
      stroke-width: 2;
    }
  }
}

.buttonIcon {
  width: var(--button-height-md, 42px);
  height: var(--button-height-md, 42px);

  & svg {
    color: var(--mantine-color-gray-3);
    stroke-width: 1.6;
    width: 24px;
    height: 24px;
  }

  &:hover {
    & svg {
      color: white;
      stroke-width: 2;
    }
  }
}
