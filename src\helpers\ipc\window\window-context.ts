import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import * as channel from "./window-channels";

export const windowContext = {
  openPath: (path: string): Promise<void> => ipcRenderer.invoke(channel.WIN_OPEN_PATH, path),
  minimize: (): Promise<void> => ipcRenderer.invoke(channel.WIN_MINIMIZE),
  maximize: (): Promise<void> => ipcRenderer.invoke(channel.WIN_MAXIMIZE),
  close: (): Promise<void> => ipcRenderer.invoke(channel.WIN_CLOSE),
};

export function exposeWindowContext() {
  contextBridge.exposeInMainWorld("electronWindow", windowContext);
}
