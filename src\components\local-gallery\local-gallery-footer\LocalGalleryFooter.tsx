import { IrfvanViewIcon } from "@/components/shared/custom-icons";
import { FooterButton, FooterButtonIcon, FooterIconHoverCard } from "@/components/shared/footer";
import Menu, { type ItemProps } from "@/components/shared/menu/Menu";
import { FetchStatus, FilterOption, SortOption } from "@/helpers/constants";
import { useAppDispatch, useAppSelector } from "@/hooks";
import {
  fetchLocalImages,
  reload,
  resetLocalImages,
  selectLocalGallery,
  setPagination,
  syncLocalImages,
} from "@/store/local-gallery-slice";
import { Kbd, Pagination } from "@mantine/core";
import { FunnelX, Info, MenuIcon, RefreshCw, Trash, Trash2 } from "lucide-react";
import { useCallback, useState } from "react";
import { FilterMenuItem, SortMenuItem } from "../local-gallery-menu";
import { LocalGalleryStats } from "../local-gallery-stats/LocalGalleryStats";

interface LocalGalleryFooterProps {
  onDelete: (ids: number[]) => void;
  onPageChange: (page: number, options?: { focusIndex?: number }) => void;
}

export function LocalGalleryFooter(props: LocalGalleryFooterProps) {
  const { onDelete, onPageChange } = props;

  const [openedMenu, setOpenedMenu] = useState(false);
  const dispatch = useAppDispatch();
  const {
    currentPage,
    selectedIds,
    sync,
    statistics: { pages },
  } = useAppSelector(selectLocalGallery);

  const getMenuItems = useCallback((): ItemProps[] => {
    return [
      { divider: true },
      {
        text: "Synchonize Local Images",
        leftSection: <RefreshCw />,
        disabled: sync.status === FetchStatus.LOADING,
        onClick: () => dispatch(syncLocalImages()),
      },
      {
        text: "Open IrfanView",
        leftSection: <IrfvanViewIcon />,
      },
      { divider: true },
      {
        text: "Delete Local Images",
        color: "red.5",
        leftSection: <Trash2 />,
        rightSection: (
          <div>
            <Kbd>Ctrl</Kbd>+<Kbd>Delete</Kbd>
          </div>
        ),
        disabled: selectedIds.length === 0,
        onClick: () => onDelete(selectedIds),
      },
      {
        text: "Reset Local Images",
        leftSection: <Trash />,
        disabled: sync.status === FetchStatus.LOADING,
        onClick: () => dispatch(resetLocalImages()),
      },
    ];
  }, [selectedIds, sync.status]);

  return {
    leftSection: (
      <FooterIconHoverCard icon={Info}>
        <LocalGalleryStats />
      </FooterIconHoverCard>
    ),
    centerSection: (
      <Pagination value={currentPage} onChange={onPageChange} total={pages} visibleFrom="xs" />
    ),
    rightSection: (
      <>
        <FooterButtonIcon
          icon={RefreshCw}
          tooltipLabel="Reload"
          onClick={() => {
            dispatch(reload());
          }}
        />
        <FooterButtonIcon
          icon={FunnelX}
          tooltipLabel="Reset filter"
          onClick={() => {
            dispatch(
              setPagination({
                where: { archived: false },
                orderBy: { created: "desc" },
                sortOptions: [SortOption.CREATED_DESC],
                filterOptions: [FilterOption.UNARCHIVED],
              })
            );
            dispatch(fetchLocalImages());
          }}
        />
        <Menu
          opened={openedMenu}
          onChange={setOpenedMenu}
          items={getMenuItems()}
          customItems={
            <>
              <Menu.Label>Filter by</Menu.Label>
              <FilterMenuItem />
              <Menu.Divider />
              <Menu.Label>Sort by</Menu.Label>
              <SortMenuItem />
            </>
          }
        >
          <FooterButton label="Options" icon={MenuIcon} />
        </Menu>
      </>
    ),
  };
}
