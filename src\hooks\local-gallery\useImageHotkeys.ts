import { useAppSelector } from "@/hooks";
import { selectLocalGallery } from "@/store/local-gallery-slice";
import { useMatches } from "@mantine/core";
import { type HotkeyItem } from "@mantine/hooks";
import { type RefObject } from "react";

enum KeyEvent {
  ARROW_RIGHT = "ArrowRight",
  ARROW_LEFT = "ArrowLeft",
  ARROW_DOWN = "ArrowDown",
  ARROW_UP = "ArrowUp",
}

type HotkeyItemParams = {
  index: number;
  setPage: (page: number, options: { focusIndex?: number }) => void;
};

export function useImageHotkeys(refs: RefObject<Map<number, HTMLDivElement | null> | null>) {
  const { currentPage } = useAppSelector(selectLocalGallery);
  const columns = useMatches({ base: 1, xs: 2, sm: 2, md: 3, lg: 4, xl: 5 });

  const goRight = ({ index, setPage }: HotkeyItemParams): HotkeyItem => {
    return [
      KeyEvent.ARROW_RIGHT,
      () => {
        const nextCard = refs.current?.get(index + 1);

        if (nextCard) {
          nextCard.focus();
        } else {
          setPage(currentPage + 1, { focusIndex: 0 });
        }
      },
    ];
  };

  const goLeft = ({ index, setPage }: HotkeyItemParams): HotkeyItem => {
    return [
      KeyEvent.ARROW_LEFT,
      () => {
        const prevCard = refs.current?.get(index - 1);

        if (prevCard) {
          prevCard.focus();
        } else {
          setPage(currentPage - 1, { focusIndex: -1 });
        }
      },
    ];
  };

  const goUp = ({ index }: HotkeyItemParams): HotkeyItem => {
    return [
      KeyEvent.ARROW_UP,
      () => {
        const prevCard = refs.current?.get(index - columns);

        if (prevCard) {
          prevCard.focus();
        } else {
          // Implement logic to handle going up when there is no previous card
        }
      },
    ];
  };

  const goDown = ({ index }: HotkeyItemParams): HotkeyItem => {
    return [
      KeyEvent.ARROW_DOWN,
      () => {
        const nextCard = refs.current?.get(index + columns);

        if (nextCard) {
          nextCard.focus();
        } else {
          // Implement logic to handle going down when there is no next card
        }
      },
    ];
  };

  const getImageCardHotkeys = (params: HotkeyItemParams): HotkeyItem[] => [
    goRight(params),
    goLeft(params),
    goDown(params),
    goUp(params),
  ];

  return { getImageCardHotkeys };
}
