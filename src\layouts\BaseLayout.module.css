.layout {
  /* Sizes */
  --app-shell-header-height: var(--header-height);
  --app-shell-footer-height: var(--footer-height);
  --app-shell-navbar-width: var(--navbar-width);

  /* Offsets */
  --app-shell-header-offset: var(--header-height);
  --app-shell-footer-offset: var(--footer-height);
  --app-shell-navbar-offset: var(--navbar-width);

  & > header[class*="mantine-AppShell-header"] {
    z-index: var(--mantine-z-index-max);
    background-color: var(--mantine-color-dark-9);
  }

  & > footer[class*="mantine-AppShell-footer"] {
    display: flex;
    background-color: var(--mantine-color-dark-8);
  }

  & > nav[class*="mantine-AppShell-navbar"] {
    justify-content: space-between;
    background-color: var(--mantine-color-dark-8);
  }

  & > main[class*="mantine-AppShell-main"] {
    & div[class*="mantine-ScrollArea-viewport"] > div > div {
      /* DIV wrapper to keep maxHeight size */
      max-height: calc(
        100dvh - var(--header-height) - var(--footer-height) - var(--app-shell-padding) * 2
      );
    }
  }
}
