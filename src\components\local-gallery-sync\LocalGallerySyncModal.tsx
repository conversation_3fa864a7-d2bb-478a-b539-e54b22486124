import { FetchStatus } from "@/helpers/constants";
import { useLocalGallerySync } from "@/hooks/local-gallery/useLocalGallerySync";
import { Button, Flex, FocusTrap, Modal, RingProgress, Text } from "@mantine/core";
import { useEffect, useState } from "react";
import classes from "./LocalGallerySyncModal.module.css";

export default function LocalGallerySyncModal() {
  const [open, setOpen] = useState(false);

  const { value, status } = useLocalGallerySync();

  useEffect(() => {
    status === FetchStatus.LOADING ? setOpen(true) : setOpen(false);
  }, [status]);

  return (
    <Modal
      size="auto"
      opened={open}
      onClose={() => setOpen(false)}
      className={classes.modal}
      withCloseButton={false}
      returnFocus
    >
      <FocusTrap.InitialFocus />
      <RingProgress
        size={350}
        thickness={20}
        transitionDuration={250}
        sections={[{ value, color: "blue" }]}
        label={
          <Text size="md" ta="center">
            Processing local images... {value}%
          </Text>
        }
      />
      <Flex className={classes.buttons}>
        <Button fullWidth variant="default" onClick={() => setOpen(false)}>
          Hide
        </Button>
        <Button
          fullWidth
          variant="filled"
          color="red"
          onClick={() => window.electronPrisma.syncLocalImagesAbort()}
        >
          Abort
        </Button>
      </Flex>
    </Modal>
  );
}
