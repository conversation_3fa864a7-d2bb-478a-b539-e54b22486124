.cardHeader {
  gap: 0;

  & > div:first-child {
    align-items: center;
    gap: 6px;
  }
}

.cardUrl {
  & div[class*="mantine-ThemeIcon-root"] {
    background-color: transparent;
    color: var(--mantine-color-dark-3);
  }

  & img[class*="mantine-Image-root"] {
    width: 24px;
    height: 24px;
  }

  & a {
    display: -webkit-box;
    overflow: hidden;
    color: darken(var(--mantine-color-anchor), 0.2);
    font-style: italic;
    font-weight: 400;
    font-size: var(--mantine-font-size-lg);
    text-overflow: ellipsis;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;

    @mixin hover {
      color: darken(var(--mantine-color-anchor), 0);
      text-decoration-color: darken(var(--mantine-color-anchor), 0.4);
      text-decoration-thickness: 3px;
      text-underline-offset: auto;
    }
  }

  &:has(a:hover) div[class*="mantine-ThemeIcon-root"] {
    filter: brightness(1.25);
  }
}

.cardDescription {
  align-items: center;
  gap: 16px;

  & p {
    display: inline-block;
    color: darken(var(--mantine-color-text), 0.3);
    font-style: italic;
    font-size: var(--mantine-font-size-sm);
  }
}
