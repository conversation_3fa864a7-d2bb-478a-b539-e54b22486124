import { ActionIcon, type ActionIconProps, type ElementProps, Flex } from "@mantine/core";
import { DeleteIcon, GripVertical, type LucideIcon, Play, RefreshCw } from "lucide-react";
import classes from "./ImageCardButtons.module.css";

export default function ImageCardButtons() {
  return (
    <Flex className={classes.buttons}>
      <ImageCardButton color="red.9" icon={DeleteIcon} />
      <Flex>
        <ImageCardButton icon={GripVertical} />
        <ImageCardButton icon={RefreshCw} />
        <ImageCardButton icon={Play} />
      </Flex>
    </Flex>
  );
}

interface ImageCardButtonProps
  extends ActionIconProps,
    ElementProps<"button", keyof ActionIconProps> {
  icon: LucideIcon;
}

function ImageCardButton(props: ImageCardButtonProps) {
  const { icon: Icon, onClick, ...rest } = props;

  return (
    <ActionIcon
      variant="subtle"
      color="blue.9"
      onClick={(event) => {
        event.stopPropagation();
        onClick?.(event);
      }}
      tabIndex={-1}
      {...rest}
      className={classes.button}
    >
      <Icon />
    </ActionIcon>
  );
}
