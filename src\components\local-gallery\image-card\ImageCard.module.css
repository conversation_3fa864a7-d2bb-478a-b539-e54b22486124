.imageCard {
  display: flex;
  position: relative;
  transform: scale(1);
  transform-origin: center;
  transition: 300ms transform allow-discrete;
  /* cursor: pointer; */
  border-radius: var(--mantine-radius-sm);
  background-repeat: no-repeat;
  /* background-color: var(--mantine-color-dark-8); */
  background-color: var(--mantine-color-black);
  height: rem(450px);

  &:hover {
    transform: scale(1.05);
  }

  &:focus {
    outline: double 6px var(--mantine-color-blue-7);
    outline-offset: 2px;
    box-shadow: inset 0px 0px 24px 0px rgba(28, 126, 214, 1);
  }

  /* LOADING OVERLAY */
  & div[class*="mantine-LoadingOverlay-root"] {
    z-index: calc(300 - 1);
    will-change: auto !important;
    border-radius: var(--mantine-radius-sm);
    user-select: none;

    & > div[class*="mantine-LoadingOverlay-loader"] {
      color: darken(var(--mantine-color-text), 0.3);
      font-style: italic;
      font-size: var(--mantine-font-size-sm);
      letter-spacing: 1.5px;
    }

    & > div[class*="mantine-LoadingOverlay-overlay"]:last-child {
      backdrop-filter: blur(10px) grayscale(100%);
      animation: 10s shine linear infinite;
      background: linear-gradient(
        -75deg,
        lighten(rgba(31, 31, 31, 0.85), 0.02) 8%,
        lighten(rgba(31, 31, 31, 0.85), 0.05) 18%,
        lighten(rgba(31, 31, 31, 0.85), 0.02) 33%
      );
      background-size: 600% 100%;

      @keyframes shine {
        to {
          background-position-x: -600%;
        }
      }
    }
  }
}
