import { SortOption } from "@/helpers/constants";
import type { LocalImageOrderBy } from "@/helpers/ipc/prisma/local-images/types";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { fetchLocalImages, selectLocalGallery, setPagination } from "@/store/local-gallery-slice";
import { Combobox, Menu, useCombobox } from "@mantine/core";
import { ArrowDownAZ } from "lucide-react";
import classes from "./SortMenuItem.module.css";

const sortOptionMap = {
  "created=desc": SortOption.CREATED_DESC,
  "created=asc": SortOption.CREATED_ASC,
  "size=asc": SortOption.SIZE_ASC,
  "size=desc": SortOption.SIZE_DESC,
  "path=asc": SortOption.PATH_ASC,
  "path=desc": SortOption.PATH_DESC,
};

const isSortOption = (value: string): value is keyof typeof sortOptionMap => value in sortOptionMap;

export function SortMenuItem() {
  const dispatch = useAppDispatch();
  const { sortOptions } = useAppSelector(selectLocalGallery);
  const combobox = useCombobox({ onDropdownClose: () => combobox.resetSelectedOption() });

  const onOptionSubmit = (value: string) => {
    if (!isSortOption(value)) return;

    const sortOption = sortOptionMap[value];
    const orderBy: LocalImageOrderBy = Object.fromEntries(new URLSearchParams(value));

    dispatch(setPagination({ orderBy, sortOptions: [sortOption] }));
    dispatch(fetchLocalImages());

    combobox.closeDropdown();
  };

  return (
    <Combobox store={combobox} withinPortal={false} onOptionSubmit={onOptionSubmit}>
      <Combobox.Target>
        <Menu.Item
          leftSection={<ArrowDownAZ />}
          rightSection={<Combobox.Chevron />}
          onClick={() => combobox.toggleDropdown()}
          closeMenuOnClick={false}
        >
          {sortOptions}
        </Menu.Item>
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Options>
          {Object.entries(sortOptionMap).map(([value, key]) => (
            <Combobox.Option key={key} value={value} className={classes.option}>
              {key}
            </Combobox.Option>
          ))}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}
