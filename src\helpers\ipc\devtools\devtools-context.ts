import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
import { DEVTOOLS_CLOSED, DEVTOOLS_OPENED, DEVTOOLS_TOGGLE } from "./devtools-channels";

export const devtoolsContext = {
  toggle: (): Promise<void> => ipcRenderer.invoke(DEVTOOLS_TOGGLE),

  onOpened: (callback: () => void) => ipcRenderer.on(DEVTOOLS_OPENED, callback),
  onClosed: (callback: () => void) => ipcRenderer.on(DEVTOOLS_CLOSED, callback),

  removeListeners: () => {
    ipcRenderer.removeAllListeners(DEVTOOLS_OPENED);
    ipcRenderer.removeAllListeners(DEVTOOLS_CLOSED);
  },
};

export function exposeDevtoolsContext() {
  contextBridge.exposeInMainWorld("electronDevtools", devtoolsContext);
}
