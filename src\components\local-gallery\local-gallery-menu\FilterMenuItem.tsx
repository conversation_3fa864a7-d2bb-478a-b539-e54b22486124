import { FilterOption } from "@/helpers/constants";
import { useLocalGalleryFilter } from "@/hooks/local-gallery/useLocalGalleryFilter";
import {
  CheckIcon,
  Combobox,
  Group,
  Input,
  Menu,
  Pill,
  PillsInput,
  useCombobox,
} from "@mantine/core";
import { ListFilter } from "lucide-react";
import { useCallback } from "react";
import classes from "./FilterMenuItem.module.css";

export function FilterMenuItem() {
  const { filterOptions, onOptionSubmit } = useLocalGalleryFilter();
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
    onDropdownOpen: () => combobox.updateSelectedOptionIndex("active"),
  });

  const Values = useCallback(() => {
    const values = filterOptions.map((item) => (
      <Pill
        key={item}
        withRemoveButton
        onRemove={() => onOptionSubmit(item)}
        className={classes.pill}
        radius="md"
        size="lg"
      >
        {item}
      </Pill>
    ));

    return values.length === 0 ? (
      <Input.Placeholder className={classes.placeholder}>No Filter selected</Input.Placeholder>
    ) : (
      values
    );
  }, [filterOptions, onOptionSubmit]);

  const Options = useCallback(() => {
    return Object.values(FilterOption).map((item) => {
      const isActive = filterOptions.includes(item);

      return (
        <Combobox.Option key={item} value={item} active={isActive} className={classes.option}>
          <Group>
            {isActive && <CheckIcon size={12} />}
            <span>{item}</span>
          </Group>
        </Combobox.Option>
      );
    });
  }, [filterOptions]);

  return (
    <Combobox store={combobox} withinPortal={false} onOptionSubmit={onOptionSubmit}>
      <Combobox.DropdownTarget>
        <Menu.Item
          pointer
          component={PillsInput}
          onClick={() => combobox.toggleDropdown()}
          className={classes.menuItem}
          leftSection={<ListFilter />}
          closeMenuOnClick={false}
        >
          <Pill.Group>
            <Values />
            <Combobox.EventsTarget>
              <PillsInput.Field type="hidden" onBlur={() => combobox.closeDropdown()} />
            </Combobox.EventsTarget>
          </Pill.Group>
        </Menu.Item>
      </Combobox.DropdownTarget>

      <Combobox.Dropdown>
        <Combobox.Options>
          <Options />
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
}
