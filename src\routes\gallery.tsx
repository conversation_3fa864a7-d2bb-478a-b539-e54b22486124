import Gallery from "@/pages/Gallery";
import { createRoute } from "@tanstack/react-router";
import { rootRoute } from "./__root";

type GallerySearch = {
  id?: string;
  url?: string;
  favicon?: string;
};

export const galleryRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/gallery",
  component: () => <Gallery />,
  validateSearch: (search: Record<string, unknown>): GallerySearch => {
    return {
      id: search.id as string,
      url: search.url as string,
      favicon: search.favicon as string,
    };
  },
});
