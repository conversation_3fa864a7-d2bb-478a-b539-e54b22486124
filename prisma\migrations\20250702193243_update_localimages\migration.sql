-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_LocalImage" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "path" TEXT NOT NULL,
    "thumbnailPath" TEXT NOT NULL,
    "created" DATETIME NOT NULL,
    "height" INTEGER NOT NULL,
    "width" INTEGER NOT NULL,
    "size" REAL NOT NULL,
    "selected" BOOLEAN NOT NULL DEFAULT false,
    "archived" BOOLEAN NOT NULL DEFAULT false,
    "portrait" BOOLEAN NOT NULL DEFAULT false
);
INSERT INTO "new_LocalImage" ("created", "height", "id", "path", "size", "thumbnailPath", "width") SELECT "created", "height", "id", "path", "size", "thumbnailPath", "width" FROM "LocalImage";
DROP TABLE "LocalImage";
ALTER TABLE "new_LocalImage" RENAME TO "LocalImage";
CREATE UNIQUE INDEX "LocalImage_path_key" ON "LocalImage"("path");
CREATE UNIQUE INDEX "LocalImage_thumbnailPath_key" ON "LocalImage"("thumbnailPath");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
