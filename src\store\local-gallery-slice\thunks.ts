import type { LocalImageData } from "@/helpers/ipc/prisma/local-images/types";
import type { AppDispatch, RootState } from "@/helpers/types";
import { createAsyncThunk } from "@reduxjs/toolkit";
import { deserializeError, isErrorLike } from "serialize-error";
import { reload } from ".";

const syncLocalImagesType = "localGallery/syncLocalImages";
const resetLocalImageType = "localGallery/resetLocalImage";
const fetchLocalImagesType = "localGallery/fetchLocalImages";
const deleteLocalImagesType = "localGallery/deleteLocalImages";

const thunk = createAsyncThunk.withTypes<{ state: RootState; dispatch: AppDispatch }>();

export const syncLocalImages = thunk<void, void>(
  syncLocalImagesType,
  async (params, { dispatch }) => {
    const result = await window.electronPrisma.syncLocalImages();

    if (isErrorLike(result)) {
      if (result.cause === "AbortError") {
        dispatch(reload());
        return;
      } else {
        throw deserializeError(result);
      }
    }

    if (typeof result === "number") {
      if (result > 0) {
        dispatch(reload());
        return;
      } else {
        return;
      }
    }
  }
);

export const fetchLocalImages = thunk<LocalImageData, number | undefined>(
  fetchLocalImagesType,
  async (page, { getState }) => {
    const { pagination, currentPage } = getState().localGallery;
    const params = { ...pagination, page: page || currentPage };

    return await window.electronPrisma.fetchLocalImages(params);
  }
);

export const deleteLocalImages = thunk<LocalImageData, number[]>(
  deleteLocalImagesType,
  async (ids, { getState }) => {
    const { pagination, currentPage } = getState().localGallery;

    await window.electronPrisma.deleteLocalImages(ids);

    return await window.electronPrisma.fetchLocalImages({ ...pagination, page: currentPage });
  }
);

export const resetLocalImages = thunk<void, void>(resetLocalImageType, async () => {
  return await window.electronPrisma.resetLocalImages();
});
