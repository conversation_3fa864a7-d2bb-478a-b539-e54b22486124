.buttons {
  justify-content: space-between;
  align-items: flex-end;
  opacity: 0;
  transition: 300ms opacity allow-discrete;
  container-type: inline-size;
  border-radius: var(--mantine-radius-sm);
  background-image: linear-gradient(
    180deg,
    rgba(0, 0, 0, 1) 0%,
    rgba(0, 0, 0, 0) 30%,
    rgba(0, 0, 0, 0) 60%,
    rgba(0, 0, 0, 1) 100%
  );
  width: 100%;

  @mixin hover {
    opacity: 1;
  }
}

.button {
  border-radius: 0;
  width: calc(var(--ai-size-xl) * 1.6);
  height: calc(var(--ai-size-xl) * 1.6);

  & svg {
    stroke-width: 1.8;
    width: 32px;
    height: 32px;
  }

  @container (max-width: 280px) {
    width: calc(var(--ai-size-xl) * 1.3);
    height: calc(var(--ai-size-xl) * 1.3);

    & svg {
      width: 24px;
      height: 24px;
    }
  }

  @mixin hover {
    & svg {
      stroke-width: 2.8;
    }
  }
}
