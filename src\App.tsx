import { router } from "@/routes/routes";
import { store } from "@/store";
import { createTheme, MantineProvider } from "@mantine/core";
import { RouterProvider } from "@tanstack/react-router";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";

const theme = createTheme({ cursorType: "pointer" });

createRoot(document.getElementById("root") as HTMLElement).render(
  <MantineProvider theme={theme} defaultColorScheme="dark">
    <Provider store={store}>
      <RouterProvider router={router} />
    </Provider>
  </MantineProvider>
);
