import { getPrisma } from "@/helpers/ipc/prisma/helpers";
import type {
  LoadLocalImageParams,
  LoadLocalImageResponse,
} from "@/helpers/ipc/prisma/local-images/types";
import { Prisma } from "@prisma/client";

export class LocalImagesLoader {
  public async load(params: LoadLocalImageParams): Promise<LoadLocalImageResponse> {
    const { id, where, orderBy } = params;

    const args = Prisma.validator<Prisma.LocalImageFindManyArgs>()({
      where,
      orderBy,
      skip: 1,
      cursor: { id },
    });

    return await getPrisma().$transaction(async (tx) => {
      const [next] = await tx.localImage.findMany({ ...args, take: 1 });
      const [prev] = await tx.localImage.findMany({ ...args, take: -1 });
      const localImage = await tx.localImage.findUnique({ where: { id } });

      return {
        current: { ...localImage, created: localImage?.created.toISOString() },
        prev: prev ? { ...prev, created: prev.created.toISOString() } : null,
        next: next ? { ...next, created: next.created.toISOString() } : null,
      } as LoadLocalImageResponse;
    });
  }
}
