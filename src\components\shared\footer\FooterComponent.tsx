import type {
  ActionIconProps,
  ButtonProps as BtnProps,
  ElementProps,
  ThemeIconProps,
} from "@mantine/core";
import { ActionIcon, Button, HoverCard, ThemeIcon, Tooltip } from "@mantine/core";
import type { LucideIcon } from "lucide-react";
import type { ComponentType } from "react";
import classes from "./FooterComponent.module.css";

type IconType = ElementProps<"div", keyof ThemeIconProps>;
type ButtonType = ElementProps<"button", keyof BtnProps>;
type ButtonIconType = ElementProps<"button", keyof ActionIconProps>;

type Variant = "icon" | "button" | "button-icon";

interface IconProps extends ThemeIconProps, IconType {
  icon: LucideIcon;
}

interface ButtonProps extends BtnProps, ButtonType {
  label: string;
  icon?: LucideIcon;
  tooltipLabel?: string;
}

interface ButtonIconProps extends ActionIconProps, ButtonIconType {
  icon: LucideIcon;
  tooltipLabel?: string;
}

type ComponentProps = IconProps | ButtonProps | ButtonIconProps;

function withFooterComponent<T extends ComponentProps>(variant: Variant): ComponentType<T> {
  return function FooterComponent(props: T) {
    if (isFooterIcon(props, variant)) {
      const { icon: Icon, ...rest } = props;

      return (
        <ThemeIcon
          size="md"
          color="blue.9"
          variant="transparent"
          {...rest}
          className={classes.icon}
        >
          <Icon />
        </ThemeIcon>
      );
    }

    if (isFooterButton(props, variant)) {
      const { tooltipLabel, icon: Icon, ...rest } = props;

      return (
        <Tooltip
          disabled={!tooltipLabel}
          label={tooltipLabel}
          position="top"
          color="gray"
          withArrow
        >
          <Button
            size="md"
            variant="filled"
            color="blue.9"
            leftSection={Icon && <Icon />}
            {...rest}
            className={classes.button}
          >
            {props.label}
          </Button>
        </Tooltip>
      );
    }

    if (isFooterButtonIcon(props, variant)) {
      const { tooltipLabel, icon: Icon, ...rest } = props;

      return (
        <Tooltip
          disabled={!tooltipLabel}
          label={tooltipLabel}
          position="top"
          color="gray"
          withArrow
        >
          <ActionIcon
            size="md"
            color="blue.9"
            variant="filled"
            {...rest}
            className={classes.buttonIcon}
          >
            <Icon />
          </ActionIcon>
        </Tooltip>
      );
    }

    return null;
  };
}

function withHoverCard(Component: ComponentType<IconProps>) {
  return function FooterIconWithHoverCardComponent(props: IconProps) {
    return (
      <HoverCard width={550} shadow="md">
        <HoverCard.Target>
          <Component {...props} />
        </HoverCard.Target>
        <HoverCard.Dropdown>{props.children}</HoverCard.Dropdown>
      </HoverCard>
    );
  };
}

function isFooterIcon(props: ComponentProps, variant: Variant): props is IconProps {
  return variant === "icon";
}

function isFooterButton(props: ComponentProps, variant: Variant): props is ButtonProps {
  return variant === "button";
}

function isFooterButtonIcon(props: ComponentProps, variant: Variant): props is ButtonIconProps {
  return variant === "button-icon";
}

export const FooterIcon = withFooterComponent<IconProps>("icon");
export const FooterIconHoverCard = withHoverCard(FooterIcon);
export const FooterButton = withFooterComponent<ButtonProps>("button");
export const FooterButtonIcon = withFooterComponent<ButtonIconProps>("button-icon");
