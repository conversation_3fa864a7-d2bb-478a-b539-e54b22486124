import ImageCardButtons from "@/components/local-gallery/image-card/ImageCardButtons";
import ImageCardCheckbox from "@/components/local-gallery/image-card/ImageCardCheckbox";
import type { LocalImage } from "@/helpers/ipc/prisma/local-images/types";
import { type ElementProps, Paper, type PaperProps } from "@mantine/core";
import { getHotkeyHandler, type HotkeyItem } from "@mantine/hooks";
import { forwardRef } from "react";
import classes from "./ImageCard.module.css";

type PaperPropsE = PaperProps & Omit<ElementProps<"div", keyof PaperProps>, "onSelect" | "id">;

interface ImageCardProps extends PaperPropsE {
  image: LocalImage;
  selected: boolean;
  hotkeys: HotkeyItem[];
  onSelect: (ids: number) => void;
  onDelete: (ids: number[]) => void;
  onArchive: (image: LocalImage) => void;
  onOpenImage: (id: number) => void;
}

const ImageCard = forwardRef<HTMLDivElement, ImageCardProps>((props, ref) => {
  const { image, hotkeys, selected, onSelect, onDelete, onOpenImage } = props;
  const { id, thumbnailPath: src, portrait } = image;

  return (
    <Paper
      ref={ref}
      tabIndex={0}
      style={{
        backgroundSize: portrait ? "cover" : "120%",
        backgroundPosition: portrait ? "top" : "center",
        backgroundImage: src ? `url(app:///${src.replaceAll("\\", "\\\\")})` : "none",
        // pointerEvents: loading ? "none" : "auto",
      }}
      onKeyDown={getHotkeyHandler([
        ...hotkeys,
        ["Enter", () => onOpenImage(id)],
        ["Space", () => onSelect(id)],
        ["Delete", () => onDelete([id])],
      ])}
      onDoubleClick={() => onOpenImage(id)}
      className={classes.imageCard}
    >
      {/* 
        <LoadingOverlay
          visible={loading}
          loaderProps={{ children: " " }}
          transitionProps={{ transition: "fade", duration: 200 }}
        /> 
      */}
      <ImageCardCheckbox checked={selected} onChange={() => onSelect(id)} />
      <ImageCardButtons />
    </Paper>
  );
});

export default ImageCard;
