import { net, protocol, type BrowserWindow } from "electron";
import { registerDevtoolsEventListeners } from "./devtools/devtools-listeners";
import { registerPrismaEventListeners } from "./prisma/prisma-listeners";
import { registerWindowEventListeners } from "./window/window-listeners";

export default function registerListeners(mainWindow: BrowserWindow) {
  // Register Protocol
  protocol.handle(
    "app",
    async (request) => await net.fetch(request.url.replace("app:///", "file:///"))
  );

  registerWindowEventListeners(mainWindow);
  registerPrismaEventListeners(mainWindow);
  registerDevtoolsEventListeners(mainWindow);
}
