import { LOCAL_ARCHIVE_DIR, LOCAL_IMAGES_DIR, LOCAL_THUMBNAILS_DIR } from "@/helpers/constants";
import { useAppSelector } from "@/hooks";
import { selectLocalGallery } from "@/store/local-gallery-slice";
import { Anchor, Divider, List, Stack, Text, Title } from "@mantine/core";

export function LocalGalleryStats() {
  const {
    currentPage,
    statistics: { selected, archived, pages, total },
  } = useAppSelector(selectLocalGallery);

  return (
    <Stack gap={1} p={10}>
      <Title order={3}>Local Images - Stats:</Title>
      <Divider my={10} />
      <List size="md">
        <StatItem label="Total" value={total} />
        <StatItem label="Archived" value={archived} />
        <StatItem label="Selected" value={selected} />
        <StatItem label="Current page" value={`${currentPage}/${pages}`} />
        <Divider my={10} />
        <LinkItem label="Images location" path={LOCAL_IMAGES_DIR} />
        <LinkItem label="Archived location" path={LOCAL_ARCHIVE_DIR} />
        <LinkItem label="Thumbnails location" path={LOCAL_THUMBNAILS_DIR} />
      </List>
    </Stack>
  );
}

function StatItem({ label, value }: { label: string; value: string | number }) {
  return (
    <List.Item>
      <Text span>
        {`${label}: `}
        <Text component="span" size="lg" fw={600}>
          {value}
        </Text>
      </Text>
    </List.Item>
  );
}

function LinkItem({ label, path }: { label: string; path: string }) {
  const onClick = () => window.electronWindow.openPath(path);

  return (
    <List.Item>
      <Text span>
        {`${label}: `}
        <Anchor fw={600} ff="monospace" component="button" onClick={onClick}>
          {path}
        </Anchor>
      </Text>
    </List.Item>
  );
}
