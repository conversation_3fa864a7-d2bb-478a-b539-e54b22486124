import { FetchStatus } from "@/helpers/constants";
import { useAppSelector } from "@/hooks";
import { selectLocalGallery } from "@/store/local-gallery-slice";
import { useEffect, useState } from "react";

export function useLocalGallerySync() {
  const { sync } = useAppSelector(selectLocalGallery);
  const [value, setValue] = useState(0);

  const { status } = sync;

  useEffect(() => {
    if (status === FetchStatus.LOADING) {
      window.electronPrisma.onSyncProgressLocalImages(({ total, progress }) => {
        setValue(Math.round((progress / total) * 100));
      });
    } else if (status === FetchStatus.SUCCEEDED || status === FetchStatus.FAILED) {
      window.electronPrisma.removeSyncProgressLocalImagesListeners();
      setValue(0);
    }
  }, [status]);

  return { value, status };
}
