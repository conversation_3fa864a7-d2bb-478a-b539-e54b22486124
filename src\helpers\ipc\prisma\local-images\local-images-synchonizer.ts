import { EXTENSIONS, LOCAL_IMAGES_DIR, LOCAL_THUMBNAILS_DIR } from "@/helpers/constants";
import { chunk, existsAsync, getPrisma } from "@/helpers/ipc/prisma/helpers";
import * as channel from "@/helpers/ipc/prisma/prisma-channels";
import { type BrowserWindow } from "electron";
import { getEventListeners } from "node:events";
import { existsSync, mkdirSync } from "node:fs";
import fsPromises from "node:fs/promises";
import { extname, join, sep } from "node:path";
import { serializeError } from "serialize-error";
import sharp from "sharp";
import { v4 as uuidv4 } from "uuid";
import type { LocalImageIdPath, LocalImageMetadata } from "./types";

export class LocalImagesSynchonizer {
  constructor(
    public window: BrowserWindow,
    public signal: AbortSignal
  ) {}

  /**
   * SYNCHRONIZES THE LOCAL IMAGES BETWEEN THE FILE SYSTEM AND THE DATABASE.
   */
  @catchException
  public async sync(): Promise<number> {
    // CREATE THUMBNAILS DIRECTORY
    if (!existsSync(LOCAL_THUMBNAILS_DIR)) {
      mkdirSync(LOCAL_THUMBNAILS_DIR, { recursive: true });
    }

    // FETCH IMAGES FROM THE FILE SYSTEM
    const images = await this.getLocalImagesFromFileSystem();

    // FETCH IMAGES FROM THE DATABASE
    const imagesDb = await getPrisma().localImage.findMany({ select: { id: true, path: true } });
    console.log(`[prisma] Found ${imagesDb.length} images in the database`);

    // DELETE IMAGES FROM THE DATABASE THAT NO LONGER EXIST IN THE FILE SYSTEM
    await this.removeNotFoundLocalImagesFromDatabase(imagesDb);

    // FILTER OUT NEW IMAGES THAT ARE NOT IN THE DATABASE
    const imagesDbPaths = new Set(imagesDb.map((image) => image.path));
    const imagesNew = images.filter((path) => !imagesDbPaths.has(path));
    console.log(`[prisma] Found ${imagesNew.length} new images in ${LOCAL_IMAGES_DIR}`);

    // CREATE NEW IMAGES IN THE DATABASE
    await this.createLocalImages(imagesNew, 8);

    return imagesNew.length;
  }

  /**
   * RETRIEVES A LIST OF IMAGE FILE PATHS FROM THE FILE SYSTEM.
   *
   * @returns An array of local image file paths.
   */
  private async getLocalImagesFromFileSystem(): Promise<string[]> {
    const images = [];

    try {
      const files = await fsPromises.readdir(LOCAL_IMAGES_DIR);
      images.push(
        ...files
          .map((file) => join(LOCAL_IMAGES_DIR, file).replaceAll(sep, "\\\\"))
          .filter((path) => EXTENSIONS.includes(extname(path)))
      );

      console.log(`[prisma] Found ${images.length} images in path ${LOCAL_IMAGES_DIR}`);
    } catch (error) {
      console.error(`[prisma] Path ${LOCAL_IMAGES_DIR} does not exist!`);
      console.error(`[prisma] ${error}`);
    }

    return images;
  }

  /**
   * REMOVES LOCAL IMAGES FROM THE DATABASE THAT NO LONGER EXIST IN THE FILE SYSTEM.
   *
   * @param images - An array of local image database records to check for missing files.
   */
  private async removeNotFoundLocalImagesFromDatabase(images: LocalImageIdPath[]): Promise<void> {
    if (images.length === 0) {
      return;
    }

    const booleanArray = await Promise.all(images.map(({ path }) => existsAsync(path)));
    const imagesDeletedFromFileSystem = images.filter((_, index) => !booleanArray[index]);

    if (imagesDeletedFromFileSystem.length > 0) {
      const ids = imagesDeletedFromFileSystem.map(({ id }) => id);
      const { count } = await getPrisma().localImage.deleteMany({ where: { id: { in: ids } } });
      console.log(`[prisma] Deleted ${count} local images from the database`);
    }
  }

  /**
   * CREATES NEW LOCAL IMAGES IN THE DATABASE IN CHUNKS, UPDATING PROGRESS.
   *
   * @param images - An array of local image filepaths to be processed and added to the database.
   * @param chunkSize - The size of each chunk to process at a time.
   */
  private async createLocalImages(images: string[], chunkSize: number): Promise<void> {
    const promiseAbort = new Promise((resolve, reject) => {
      this.signal.onabort = () => reject(new Error("AbortError", { cause: "AbortError" }));
    });
    const prisma = getPrisma();

    let progress = 0;

    for (const imagesNewChunk of chunk(images, chunkSize)) {
      const data = await Promise.race([
        Promise.all(imagesNewChunk.map((imagePath) => this.createLocalImageData(imagePath))),
        promiseAbort,
      ]);

      const { count } = await prisma.localImage.createMany({ data: data as LocalImageMetadata[] });

      progress += count;
      this.window.webContents.send(channel.SYNC_PROGRESS_LOCAL_IMAGES, {
        total: images.length,
        progress,
      });
      console.log(`[prisma] Created ${progress}/${images.length} new local images`);
    }
  }

  /**
   * GENERATES METADATA AND A THUMBNAIL FOR A LOCAL IMAGE FILE.
   *
   * @param imagePath - The absolute path to the image file.
   * @returns Metadata of the processed image (original image path, thumbnail path, etc.)
   */
  private async createLocalImageData(imagePath: string): Promise<LocalImageMetadata> {
    const { width, height } = await sharp(imagePath).metadata();
    const { size, birthtimeMs } = await fsPromises.stat(imagePath);
    const outputPath = join(LOCAL_THUMBNAILS_DIR, uuidv4());

    sharp(imagePath)
      .resize({ fit: "contain", kernel: "nearest", width: 500 })
      .webp()
      .toFile(outputPath);

    return {
      path: imagePath,
      thumbnailPath: outputPath,
      width,
      height,
      size,
      created: new Date(birthtimeMs),
      portrait: width < height,
    };
  }
}

function catchException(target: object, propertyKey: string, descriptor: PropertyDescriptor): void {
  const method = descriptor.value;

  if (target.constructor.name !== LocalImagesSynchonizer.name) {
    throw new Error(`Decorator can only be applied to LocalImagesSynchonizer class methods`);
  }

  descriptor.value = async function (this: LocalImagesSynchonizer) {
    try {
      return await method?.apply(this);
    } catch (error) {
      return serializeError(error);
    } finally {
      // Remove abort listeners
      const onAbortListener = getEventListeners(this.signal, "abort");
      onAbortListener.map((listener) =>
        this.signal.removeEventListener("abort", listener as EventListenerOrEventListenerObject)
      );
    }
  };
}
