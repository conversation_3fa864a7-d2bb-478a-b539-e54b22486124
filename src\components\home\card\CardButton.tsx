import type { ActionIconProps, ElementProps } from "@mantine/core";
import { ActionIcon } from "@mantine/core";
import type { LucideIcon } from "lucide-react";
import classes from "./CardButton.module.css";

interface CardButtonProps extends ActionIconProps, ElementProps<"button", keyof ActionIconProps> {
  icon: LucideIcon;
}

export default function CardButton(props: CardButtonProps) {
  const { icon: Icon, ...actionIconProps } = props;

  return (
    <ActionIcon variant="subtle" color="gray.5" {...actionIconProps} className={classes.cardButton}>
      <Icon />
    </ActionIcon>
  );
}
