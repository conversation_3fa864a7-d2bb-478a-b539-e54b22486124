import { useRef } from "react";

export function useImageRefs() {
  const refs = useRef<Map<number, HTMLDivElement | null>>(null);

  const getMap = () => {
    if (!refs.current) {
      refs.current = new Map();
    }

    return refs.current;
  };

  const setRef = (card: HTMLDivElement | null, index: number) => {
    const map = getMap();
    map.set(index, card);

    return () => {
      map.delete(index);
    };
  };

  const setFocus = (index: number) => {
    const map = refs.current;

    if (!map) return;

    setTimeout(() => {
      if (index === -1) {
        Array.from(map.values()).pop()?.focus();
      } else {
        map.get(index)?.focus();
      }
    }, 50);
  };

  return { refs, setRef, setFocus };
}
