import { type BrowserWindow, ipc<PERSON>ain } from "electron";
import { LocalImagesLoader, LocalImagesManager, LocalImagesSynchonizer } from "./operations";
import * as channel from "./prisma-channels";

export function registerPrismaEventListeners(window: BrowserWindow) {
  ipcMain.handle(channel.SYNC_LOCAL_IMAGES, async () => {
    const controller = new AbortController();

    ipcMain.once(channel.SYNC_ABORT_LOCAL_IMAGES, () => controller.abort());

    return await new LocalImagesSynchonizer(window, controller.signal).sync();
  });

  ipcMain.handle(channel.FETCH_LOCAL_IMAGES, async (event, params) => {
    return await new LocalImagesManager().fetch(params);
  });

  ipcMain.handle(channel.DELETE_LOCAL_IMAGES, async (event, params) => {
    return await new LocalImagesManager().delete(params);
  });

  ipcMain.handle(channel.UPDATE_LOCAL_IMAGE, async (event, params) => {
    return await new LocalImagesManager().update(params);
  });

  ipcMain.handle(channel.RESET_LOCAL_IMAGES, async () => {
    return await new LocalImagesManager().reset();
  });

  ipcMain.handle(channel.LOAD_LOCAL_IMAGE, async (event, params) => {
    return await new LocalImagesLoader().load(params);
  });
}
