import Navbar from "@/components/shared/navbar/Navbar";
import TitleBar from "@/components/shared/title-bar/TitleBar";
import { AppContext } from "@/context";
import { AppShell } from "@mantine/core";
import { Outlet } from "@tanstack/react-router";
import { createRef } from "react";
import classes from "./BaseLayout.module.css";

export default function BaseLayout() {
  const viewport = createRef<HTMLDivElement>();

  return (
    <AppContext value={{ viewport }}>
      <AppShell withBorder={false} className={classes.layout}>
        <AppShell.Header>
          <TitleBar />
        </AppShell.Header>
        <AppShell.Navbar>
          <Navbar />
        </AppShell.Navbar>
        <Outlet />
      </AppShell>
    </AppContext>
  );
}
