.localImage {
  & div[class*="mantine-Modal-inner"] {
    padding-top: calc(var(--header-height) + 2vh);
    padding-bottom: 5vh;
  }

  & div[class*="mantine-Modal-body"] {
    position: relative;
  }

  & div[class*="mantine-Modal-content"] {
    overflow: hidden;
  }

  & div[class*="mantine-Modal-body"] {
    background-color: black;
    padding: 0;
  }

  & img {
    width: calc(100vw - 10vw);
    height: calc(100vh - var(--header-height) - 5vh - 2vh);
    object-fit: contain;
  }
}
