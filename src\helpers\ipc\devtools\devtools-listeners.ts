import { type <PERSON><PERSON>er<PERSON>ind<PERSON>, ipc<PERSON><PERSON> } from "electron";
import { DEVTOOLS_CLOSED, DEVTOOLS_OPENED, DEVTOOLS_TOGGLE } from "./devtools-channels";

export function registerDevtoolsEventListeners(window: <PERSON>rowserWindow) {
  ipcMain.handle(DEVTOOLS_TOGGLE, () =>
    window.webContents.isDevToolsOpened()
      ? window.webContents.closeDevTools()
      : window.webContents.openDevTools()
  );

  window.webContents.on("devtools-opened", () => window.webContents.send(DEVTOOLS_OPENED));
  window.webContents.on("devtools-closed", () => window.webContents.send(DEVTOOLS_CLOSED));
}
