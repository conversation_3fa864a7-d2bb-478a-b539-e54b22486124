import type { AnchorProps } from "@mantine/core";
import { Anchor, Flex, Image, Stack, Text, ThemeIcon } from "@mantine/core";
import { createLink, type LinkComponent } from "@tanstack/react-router";
import { Globe } from "lucide-react";
import { forwardRef } from "react";
import classes from "./CardHeader.module.css";

interface CardHeaderProps {
  id: string;
  url: string;
  date: string;
  icon?: string;
}

export default function CardHeader(props: CardHeaderProps) {
  const { id, url, icon, date } = props;

  const favicon = icon ? `data:image/png;base64, ${icon}` : undefined;

  return (
    <Stack className={classes.cardHeader}>
      <Flex className={classes.cardUrl}>
        <ThemeIcon>{icon ? <Image src={favicon} /> : <Globe />}</ThemeIcon>
        <CardLink to="/gallery" search={{ id, favicon, url }}>
          {url}
        </CardLink>
      </Flex>
      <Flex className={classes.cardDescription}>
        <Text>{date}</Text>
      </Flex>
    </Stack>
  );
}

// CardLink

const CardLinkComponent = forwardRef<HTMLAnchorElement, Omit<AnchorProps, "href">>((props, ref) => {
  return <Anchor ref={ref} {...props} />;
});

const CardLinkComponentTS = createLink(CardLinkComponent);

const CardLink: LinkComponent<typeof CardLinkComponent> = (props) => {
  return <CardLinkComponentTS {...props} />;
};
