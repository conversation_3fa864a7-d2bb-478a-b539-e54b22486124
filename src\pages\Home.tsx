import Card from "@/components/home/<USER>/Card";
import { FooterButton } from "@/components/shared/footer";
import PageLayout from "@/layouts/PageLayout";
import { RefreshCw } from "lucide-react";
import { useCallback } from "react";

// TODO: Replace with actual data fetching logic
const data = [
  {
    id: "1",
    url: "https://www.google.com/",
    icon: "iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAnFBMVEVHcEz7/Pz7/Pz09fX8/f39/v77+/zy8/Py8vP7+/v+/v7////v7/D6+vr09fXv8PHw8vL///9JivQ5qlfsUEP8wBDrSz49rForp01BhfXqPy/4+Pg2gfTU4f3va2CMsff4w7/96ePi8eeLypqf06z61tT0nZe/4chvvoL+7cNPsW7vZ0XV69r90mL+3I4+nZ2NtUmpxPr2oCXFvkFRVdGkAAAAEXRSTlMAfMttPu3cMRi3m0qUUl6msZyb5rsAAAMHSURBVFiF5VfbdqIwFEVBBVu13AOxVBSVaq/T//+3SUIgJxcQu9Y8zW6fTPbOPhfCwbL+HRbOZLn0vOVy4izuJru2N/MBZp7t3kFfTX0DpquR9PXMRGc+1iPozryPTjF3bvGXQ3SK5SDdHTyemxjIpnObTtEbxmoc3/cfzPwHbSMujwQlHqewUDaVpyJKcoIkKk6lsmhoTVfesbvmSRIxJAnRue7kdT2TUvPtirxl0z+KvDjCHVOVP4Grpzxq6Y0C08hPcM+kP4Cy4KdHXQgJN9EfhAf4keDTo0EUUh68ngpg7fTWQq7kcWE2APyT5EdFEZFCmvjQAshAlz9CL3as/CWtic6HWRAlKLvz8wI0z7Ew8EEhxA3SBSDXzMdHlU0w0yMo4yvnGw7U0cZgd798xPE10c/vg63VICb4Q/JXDLAAPDUFr3GjMC4AkQQYAcMVbHrWIRbVNnznAu9AYJtl2VbCi1hsmlHchJwfv0KBQEaWBWLR+YUAgSqw0gTKXoGM/ovF5m580AQGQwg0gfEhZKoDp6cKH1Ag68ANAIGF8ii0ffADBF4EmIEgA2V0jZ0Yf6E334BvRg+2390vbSd2Nzpm/DQMa5MAT8FWdGJ7t4v7hCThJyVAZ53/vG1SGIg3XXujiCy+xl8hFUjDvcrHDT3LtE6GTxPmfBKFqsD5wVZ7liw4lpxRyhVQBen7gJcAdoEYVsBroeYWSB4uZx4tfjugT+4AGgAvBjHZ4E6AmEjrQ1Ud6gtCYXhpAgAZmAs+nE26IFgmEKJkChQyBTBsSGPjHCqEKf8LQ6bCgT6z4NlsQB6vqAIkAgXAV0ct8Hb031Jmu/EQdkroAkvrWQrgJIVrpB+PDj7covKVGXFfIyTTa7mzDFOWNOQQieqCOlwqpTEnOt+yHn0F+3N1IKjO2oPxaOJb1kbd14eNmW/wYEbP+RS2PtVqwHY/nzxWN+f9+a0PsMdBE3jAfgv3qVcCe+M+3RZmCfw0/svPXXtYEsHYW9/z4ch82JvpDBPMphv7/k/X/wl/AdK1zOhq9FqQAAAAAElFTkSuQmCC",
    date: "4 days ago",
    unvisited: 43,
  },
  {
    id: "2",
    url: "https://stackoverflow.com/questions/8499633/how-to-display-base64-images-in-html",
    icon: "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYZDQwMDRsUFRAWIB0iJSAdHR8dKCggHR01JyUVLTElJSkrLjouHR81ODMsNygtLisBCgoKDQwNGg8PGi0lICYvNzcyMS0tKy03NS0tNSs1NSsrLS0tLTctKy0tLSsrKysrKysrKysrKysrKysrKysrK//AABEIACAAIAMBEQACEQEDEQH/xAAZAAACAwEAAAAAAAAAAAAAAAAEBgECAwf/xAAlEAABAwQBAwUBAAAAAAAAAAABAgMFAAQGERIUIjEyQUJRYSH/xAAYAQADAQEAAAAAAAAAAAAAAAAAAQUCBP/EAB4RAAICAgMBAQAAAAAAAAAAAAECAAMRIgQSIRNB/9oADAMBAAIRAxEAPwAO/gIO0cbceY4tjeyVVXaqtfTLj0VL6RMDaYq72gJJPjurPWkxdeOZZ2AhWo3qHGNAH1cqZqrC5xA0VBMkSl3isLLWHKySEr+Kwd6pGit11ibjVWLrCMstHJOx6VlWlr8GtXKXXAm+QhdeonP5HGr2KbTcOuq4hQ9/2uF6WT2TX47VjJj5JtOXmLFhlWlqSnRrtcFqsCUbFLU4EjHLUwEWerdJ+/yipfmvsKE+KbTLJJByytOoZJ5p8apXOVGRC9yi5EQ7/IZGSbSw+FFBUN9v7XC1zP4ZNe93GDHWTvXbTGS6z60pGq7XYrXkShY5WnIk45IOy8Zq+QT9796KnLrtChzYm0CuciiXi3ze5N++xWDchmG5FR/ZRczAhAVyT/fHbS+tURupxJdnYxVghC3dpWf4NUG1OsDfX1wTM7vKo+1teNiQpY+IGqTchANYm5SKus//2Q==",
    date: "100 days ago",
    unvisited: 0,
  },
  {
    id: "33",
    url: "https://www.reddit.com/r/css/comments/xgwrvi/typography_any_pure_css_way_for_the_paragraph/",
    icon: "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwEBAQkIBwgKCgkLDQoPDQwMARsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3NzctNzc3Nzc3Nzc3NTcrMzA3N//AABEIABAAEAMBEQACEQEDEQH/xAAVAAEBAAAAAAAAAAAAAAAAAAAFAv/EACoQAAECAwYEBwAAAAAAAAAAAAECAwYRIQAEBQgSEwcxYXEUFhciMkFR/8QAGQEAAQUAAAAAAAAAAAAAAAAAAwECBAUG/8QAHhEAAgMAAQUAAAAAAAAAAAAAAQIAAxESMUFRccH/2gAMAwEAAhEDEQA/ABE3TMNGEMqvTZmyA6VL8yD26ZTn+Go72vtUHDMzxcjR0jsU4HA+VeArztNY4tTaGCL0qA0+HmoJPyl1l3sCu0v4+yVdStfY++0CaxuDYG4UO4ebulTbu5vExTIqVTSRSmmXKs62MQOW7ABiF45JiSIYkze8UH3wpxpp1LCRd/UUqSNKQOn2J8rNRFRci2WPY+9BP//Z",
    date: "4 minutes ago",
    unvisited: 252,
  },
  {
    id: "4",
    url: "https://www.youtube.com/watch?v=U_H5dYNNHtg",
    icon: undefined,
    date: "4 days ago",
    unvisited: 0,
  },
];

export default function Home() {
  const setFooter = useCallback(() => {
    return {
      centerSection: <FooterButton label="Reload" icon={RefreshCw} />,
    };
  }, []);

  return (
    <PageLayout footer={setFooter()}>
      {data.map((item, index) => (
        <Card key={index} {...item} />
      ))}
    </PageLayout>
  );
}
