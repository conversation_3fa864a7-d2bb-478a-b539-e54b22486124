.footer {
  padding-inline: calc(var(--mantine-spacing-sm) * 1.2);
  width: 100%;

  & > :first-child {
    display: flex;
    flex-grow: 1;
    flex-basis: 0;
    justify-content: left;
    align-items: center;
    gap: calc(var(--mantine-spacing-xs) * 0.6);
  }

  & > :nth-child(2) {
    display: flex;
    justify-content: left;
    align-items: center;
  }

  & > :last-child {
    display: flex;
    flex-grow: 1;
    flex-basis: 0;
    justify-content: right;
    align-items: center;
    gap: calc(var(--mantine-spacing-xs) * 0.6);
  }
}
