-- CreateTable
CREATE TABLE "LocalImage" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "path" TEXT NOT NULL,
    "thumbnailPath" TEXT NOT NULL,
    "created" DATETIME NOT NULL,
    "height" INTEGER NOT NULL,
    "width" INTEGER NOT NULL,
    "size" REAL NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "LocalImage_path_key" ON "LocalImage"("path");

-- CreateIndex
CREATE UNIQUE INDEX "LocalImage_thumbnailPath_key" ON "LocalImage"("thumbnailPath");
