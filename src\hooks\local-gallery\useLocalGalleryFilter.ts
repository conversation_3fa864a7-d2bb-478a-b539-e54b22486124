import { FilterOption } from "@/helpers/constants";
import type { LocalImageWhere } from "@/helpers/ipc/prisma/local-images/types";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { fetchLocalImages, selectLocalGallery, setPagination } from "@/store/local-gallery-slice";

export function useLocalGalleryFilter() {
  const dispatch = useAppDispatch();
  const { filterOptions } = useAppSelector(selectLocalGallery);

  const onOptionSubmit = (value: string) => {
    if (!isFilterOption(value)) return;

    let newFilterOptions: FilterOption[] = [];

    if (filterOptions.includes(value)) {
      newFilterOptions = filterOptions.filter((v) => v !== value);
    } else {
      newFilterOptions = [...filterOptions, value];

      // Ensure SELECTED and UNSELECTED are mutually exclusive
      if (value === FilterOption.SELECTED) {
        newFilterOptions = newFilterOptions.filter((v) => v !== FilterOption.UNSELECTED);
      } else if (value === FilterOption.UNSELECTED) {
        newFilterOptions = newFilterOptions.filter((v) => v !== FilterOption.SELECTED);
      }

      // Ensure ARCHIVED and UNARCHIVED are mutually exclusive
      if (value === FilterOption.ARCHIVED) {
        newFilterOptions = newFilterOptions.filter((v) => v !== FilterOption.UNARCHIVED);
      } else if (value === FilterOption.UNARCHIVED) {
        newFilterOptions = newFilterOptions.filter((v) => v !== FilterOption.ARCHIVED);
      }

      // Ensure PORTRAIT and LANDSCAPE are mutually exclusive
      if (value === FilterOption.PORTRAIT) {
        newFilterOptions = newFilterOptions.filter((v) => v !== FilterOption.LANDSCAPE);
      } else if (value === FilterOption.LANDSCAPE) {
        newFilterOptions = newFilterOptions.filter((v) => v !== FilterOption.PORTRAIT);
      }
    }

    const where: LocalImageWhere = {};

    for (const filterOption of newFilterOptions) {
      switch (filterOption) {
        case FilterOption.SELECTED:
        case FilterOption.UNSELECTED:
          where.selected = filterOption === FilterOption.SELECTED;
          break;
        case FilterOption.ARCHIVED:
        case FilterOption.UNARCHIVED:
          where.archived = filterOption === FilterOption.ARCHIVED;
          break;
        case FilterOption.PORTRAIT:
        case FilterOption.LANDSCAPE:
          where.portrait = filterOption === FilterOption.PORTRAIT;
          break;
      }
    }

    dispatch(setPagination({ where, filterOptions: newFilterOptions, currentPage: 1 }));
    dispatch(fetchLocalImages());
  };

  return { filterOptions, onOptionSubmit };
}

function isFilterOption(value: string): value is FilterOption {
  return Object.values(FilterOption).includes(value as FilterOption);
}
