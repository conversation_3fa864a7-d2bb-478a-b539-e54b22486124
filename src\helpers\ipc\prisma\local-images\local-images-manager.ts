import { LOCAL_ARCHIVE_DIR, LOCAL_IMAGES_DIR, PAGE_SIZE } from "@/helpers/constants";
import { getPrisma } from "@/helpers/ipc/prisma/helpers";
import { type Prisma } from "@prisma/client";
import { existsSync, mkdirSync } from "node:fs";
import fsPromises from "node:fs/promises";
import path from "node:path";
import type { FetchLocalImagesParams, LocalImageData, LocalImageWhere } from "./types";

export class LocalImagesManager {
  constructor(private prisma = getPrisma()) {}

  public async fetch(params: FetchLocalImagesParams): Promise<LocalImageData> {
    const { where, orderBy, page } = params;

    const localImagesCount = await this.prisma.localImage.count({ where });
    const localImages = await this.prisma.localImage.findMany({
      where,
      orderBy,
      skip: (page - 1) * PAGE_SIZE,
      take: PAGE_SIZE,
    });

    const localImagesSerialized = localImages.map((localImages) => ({
      ...localImages,
      created: localImages.created.toISOString(),
    }));

    const localImagesSelected = await this.prisma.localImage.findMany({
      where: { selected: true },
      select: { id: true },
    });

    const localImagesArchived = await this.prisma.localImage.count({
      where: { archived: true },
    });

    const statistics = {
      selected: localImagesSelected.length,
      archived: localImagesArchived,
      pages: Math.ceil(localImagesCount / PAGE_SIZE),
      total: localImagesCount,
    };

    return {
      data: localImagesSerialized,
      selectedIds: localImagesSelected.map((image) => image.id),
      statistics,
    };
  }

  public async delete(ids: number[]): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      const deletedPaths = await tx.localImage.findMany({
        where: { id: { in: ids } },
        select: { path: true, thumbnailPath: true },
      });

      for (const { path, thumbnailPath } of deletedPaths) {
        await fsPromises.rm(thumbnailPath, { force: true });
        await fsPromises.rm(path, { force: true });
        console.log(`[prisma] Deleted image ${path}`);
      }

      const { count } = await tx.localImage.deleteMany({ where: { id: { in: ids } } });
      console.log(`[prisma] Deleted ${count} images from the database`);
    });
  }

  public async update(params: Prisma.LocalImageUpdateManyArgs): Promise<void> {
    if ("archived" in params.data) {
      if (params.where && params.where.id) {
        const ids: number[] = [];

        if (this.isIntFilter(params.where.id) && params.where.id.in) {
          ids.push(...params.where.id.in);
        } else if (typeof params.where.id === "number") {
          ids.push(params.where.id);
        } else {
          throw new Error("Invalid ID filter in WHERE clause: expected a number or an 'in' array.");
        }

        if (params.data.archived === true) {
          await this.archive(ids);
        } else {
          await this.unarchive(ids);
        }
      } else {
        throw new Error("Archiving requires a valid numeric ID in the WHERE clause.");
      }
    } else {
      await this.prisma.localImage.updateMany(params);
    }
  }

  public async reset(): Promise<void> {
    const { count } = await this.prisma.localImage.deleteMany({ where: { archived: false } });
    console.log(`[prisma] Deleted ${count} images from the database`);
  }

  public async archive(ids: number[]): Promise<void> {
    const images = await this.prisma.localImage.findMany({ where: { id: { in: ids } } });

    await this.prisma.$transaction(async (tx) => {
      for (const { id, path: oldPath } of images) {
        const newPath = await this.moveFile(
          oldPath,
          path.join(LOCAL_ARCHIVE_DIR, path.basename(oldPath))
        );

        await tx.localImage.update({ where: { id }, data: { path: newPath, archived: true } });
        console.log(`[prisma] (Archived) Moved image to path ${newPath}`);
      }
    });
  }

  public async unarchive(ids: number[]): Promise<void> {
    const images = await this.prisma.localImage.findMany({ where: { id: { in: ids } } });

    await this.prisma.$transaction(async (tx) => {
      for (const { id, path: oldPath } of images) {
        const newPath = await this.moveFile(
          oldPath,
          path.join(LOCAL_IMAGES_DIR, path.basename(oldPath))
        );

        await tx.localImage.update({ where: { id }, data: { path: newPath, archived: false } });
        console.log(`[prisma] (Unarchived) Moved image to path ${newPath}`);
      }
    });
  }

  private async moveFile(oldPath: string, newPath: string) {
    if (!existsSync(LOCAL_ARCHIVE_DIR)) {
      mkdirSync(LOCAL_ARCHIVE_DIR, { recursive: true });
    }

    await fsPromises.rename(oldPath, newPath);

    return newPath.replaceAll(path.sep, "\\\\");
  }

  private isIntFilter(
    field: NonNullable<LocalImageWhere>["id"]
  ): field is Prisma.IntFilter<"LocalImage"> {
    if (typeof field !== "number" && typeof field !== "undefined") {
      return "in" in field;
    } else {
      return false;
    }
  }
}
