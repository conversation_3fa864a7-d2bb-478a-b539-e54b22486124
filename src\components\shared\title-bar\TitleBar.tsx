import { FetchStatus } from "@/helpers/constants";
import { closeWindow, maximizeWindow, minimizeWindow } from "@/helpers/ipc/window/window-helpers";
import { useLocalGallerySync } from "@/hooks/local-gallery/useLocalGallerySync";
import type { ActionIconProps, ButtonProps, ElementProps } from "@mantine/core";
import { ActionIcon, Divider, Flex, RingProgress, Switch, Text, Tooltip } from "@mantine/core";
import type { LucideIcon } from "lucide-react";
import { Minus, PictureInPicture2, Square, X } from "lucide-react";
import classes from "./TitleBar.module.css";

interface TitleBarButtonProps extends ActionIconProps, ElementProps<"button", keyof ButtonProps> {
  icon: LucideIcon;
  iconColor?: string;
  iconSize?: number;
}

export default function TitleBar() {
  const { value, status } = useLocalGallerySync();

  return (
    <Flex className={classes.root}>
      <Flex align="center">
        <Text className={classes.title}>Viper App</Text>
        {status === FetchStatus.LOADING && (
          <Tooltip
            label={`Processing local images... ${value}%`}
            color="dark.9"
            position="bottom"
            openDelay={200}
          >
            <RingProgress
              size={30}
              thickness={3}
              sections={[{ value, color: "yellow" }]}
              style={{ WebkitAppRegion: "no-drag" }}
            />
          </Tooltip>
        )}
      </Flex>
      <Flex>
        <TitleBarSwitch label="Always on top" />
        <Divider orientation="vertical" mx={8} />
        <TitleBarButton icon={Minus} onClick={minimizeWindow} />
        <TitleBarButton icon={Square} iconSize={12} onClick={maximizeWindow} />
        <TitleBarButton icon={X} iconSize={20} onClick={closeWindow} />
      </Flex>
    </Flex>
  );
}

function TitleBarButton(props: TitleBarButtonProps) {
  const { icon: Icon, iconColor, iconSize, ...rest } = props;

  return (
    <ActionIcon color="gray.5" variant="subtle" className={classes.button} tabIndex={-1} {...rest}>
      <Icon size={iconSize ?? 16} color={iconColor} strokeWidth={1} />
    </ActionIcon>
  );
}

function TitleBarSwitch({ label }: { label: string }) {
  return (
    <Tooltip label={label} color="dark.9" position="bottom" openDelay={200} refProp="rootRef">
      <Switch
        onLabel={<PictureInPicture2 size={14} strokeWidth={1} />}
        className={classes.switch}
        size="xs"
      />
    </Tooltip>
  );
}
