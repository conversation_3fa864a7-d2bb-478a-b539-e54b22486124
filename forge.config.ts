import { MakerZIP } from "@electron-forge/maker-zip";
import { FusesPlugin } from "@electron-forge/plugin-fuses";
import { VitePlugin } from "@electron-forge/plugin-vite";
import type { ForgeConfig } from "@electron-forge/shared-types";
import { FuseV1Options, FuseVersion } from "@electron/fuses";
import { promises } from "node:fs";
import { join, sep } from "node:path";

const externalModules = [
  ".prisma",
  "@prisma",
  "sharp",
  "@img",
  "detect-libc",
  "semver",
  "color",
  "color-string",
  "color-name",
  "simple-swizzle",
  "color-convert",
];

const config: ForgeConfig = {
  packagerConfig: {
    asar: true,
    extraResource: externalModules.map((name) => `./node_modules/${name}`),
  },
  rebuildConfig: {},
  makers: [new MakerZIP({}, ["darwin"])],
  plugins: [
    new VitePlugin({
      build: [
        { entry: "src/main.ts", config: "vite.main.config.ts", target: "main" },
        { entry: "src/preload.ts", config: "vite.preload.config.ts", target: "preload" },
      ],
      renderer: [{ name: "main_window", config: "vite.renderer.config.ts" }],
    }),
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: true,
    }),
  ],
  hooks: {
    postPackage: async (forgeConfig, options) => {
      const resourcesPath =
        process.platform === "darwin"
          ? join(options.outputPaths[0], "viper-app.app", "Contents", "Resources")
          : join(options.outputPaths[0], "resources");

      const copy = async (source: string) => {
        const name = source.split(sep).pop() as string;
        const destination = join(resourcesPath, "node_modules", name);
        await promises.cp(source, destination, { recursive: true });
      };

      await Promise.all(externalModules.map((name) => copy(join(resourcesPath, name))));
      await Promise.all(
        externalModules.map((name) =>
          promises.rm(join(resourcesPath, name), { recursive: true, force: true })
        )
      );
    },
  },
};

export default config;
