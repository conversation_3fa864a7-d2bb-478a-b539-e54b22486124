export const PAGE_SIZE = 15;

export const LOCAL_IMAGES_DIR = import.meta.env.VITE_LOCAL_IMAGES;
export const LOCAL_ARCHIVE_DIR = import.meta.env.VITE_LOCAL_IMAGES_ARCHIVE;
export const LOCAL_THUMBNAILS_DIR = import.meta.env.VITE_LOCAL_IMAGES_THUMBNAILS;
export const EXTENSIONS = [".jpg", ".jpeg", ".png"];

export enum FetchStatus {
  IDLE = "idle",
  FAILED = "failed",
  LOADING = "loading",
  SUCCEEDED = "succeeded",
}

export enum SortOption {
  CREATED_DESC = "Date created (Newest)",
  CREATED_ASC = "Date created (Oldest)",
  SIZE_DESC = "Size (Descending)",
  SIZE_ASC = "Size (Ascending)",
  PATH_DESC = "Name (Z-A)",
  PATH_ASC = "Name (A-Z)",
}

export enum FilterOption {
  UNSELECTED = "Unselected",
  SELECTED = "Selected",
  ARCHIVED = "Archived",
  UNARCHIVED = "Unarchived",
  PORTRAIT = "Portrait",
  LANDSCAPE = "Landscape",
  // FAVORITE = "Favorite",
}
