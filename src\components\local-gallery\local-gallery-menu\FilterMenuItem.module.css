.menuItem {
  padding: 0;

  & div[class*="mantine-PillsInput-wrapper"] {
    width: 100%;
  }

  & div[class*="mantine-PillsInput-input"] {
    display: flex;
    align-items: center;
    border: 0;
    min-height: 48px;

    &:hover {
      background-color: var(--mantine-color-dark-4);

      & svg {
        stroke-width: 2;
      }
    }
  }
}

.option {
  & span {
    font-weight: 350;
    font-size: var(--mantine-font-size-md);
    letter-spacing: 1.1px;
  }

  & div[class*="mantine-Group-root"] {
    gap: var(--mantine-spacing-xs);
  }
}

.pill {
  font-weight: 350;
  font-size: var(--mantine-font-size-md);
  letter-spacing: 1.1px;
}

.placeholder {
  color: var(--mantine-color-placeholder);
  font-weight: 350;
  font-size: var(--mantine-font-size-md);
  letter-spacing: 1.1px;
}
