.card {
  display: flex;
  position: relative;
  justify-content: space-between;
  align-items: center;
  border-radius: calc(var(--paper-radius) * 2);
  background-color: var(--mantine-color-dark-8);
  padding-inline: calc(32px * 2);
  width: 100%;
  max-width: 1200px;
  height: 100px;

  /* LOADING OVERLAY */
  & div[class*="mantine-LoadingOverlay-root"] {
    z-index: calc(300 - 1);
    will-change: auto !important;
    user-select: none;

    & > div[class*="mantine-LoadingOverlay-loader"] {
      color: darken(var(--mantine-color-text), 0.3);
      font-style: italic;
      font-size: var(--mantine-font-size-sm);
      letter-spacing: 1.5px;
    }

    & > div[class*="mantine-LoadingOverlay-overlay"]:last-child {
      backdrop-filter: blur(3px) grayscale(100%);
      animation: 2.2s shine linear infinite;
      background: rgba(31, 31, 31, 0.85);
      background: linear-gradient(
        110deg,
        lighten(rgba(31, 31, 31, 0.85), 0.02) 8%,
        lighten(rgba(31, 31, 31, 0.85), 0.05) 18%,
        lighten(rgba(31, 31, 31, 0.85), 0.02) 33%
      );
      background-size: 200% 100%;

      @keyframes shine {
        to {
          background-position-x: -200%;
        }
      }
    }
  }

  /* BADGE */
  & div[class*="mantine-Badge-root"] {
    position: absolute;
    top: 8px;
    left: 8px;
    border: none;
    background: linear-gradient(
      45deg,
      var(--mantine-color-red-9) 0%,
      var(--mantine-color-grape-9) 100%
    );
    color: lighten(var(--mantine-color-text), 0.8);
    font-weight: 600;
  }
}
