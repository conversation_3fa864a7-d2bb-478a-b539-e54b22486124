import Home from "@/pages/Home";
import { Text } from "@mantine/core";
import { createMemoryHistory, createRoute, createRouter } from "@tanstack/react-router";
import { rootRoute } from "./__root";
import { galleryRoute } from "./gallery";
import { localGalleryRoute } from "./local-gallery";

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const history = createMemoryHistory({ initialEntries: ["/"] });

export const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: () => <Home />,
});

export const settingsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/settings",
  component: () => <Text>Settings</Text>,
});

const routeTree = rootRoute.addChildren([
  homeRoute,
  galleryRoute,
  localGalleryRoute,
  settingsRoute,
]);

export const router = createRouter({ routeTree, history });
